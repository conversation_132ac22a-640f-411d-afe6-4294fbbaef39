// API Response Types
export interface ApiAgenticTickerEntry {
  owner: {
    agentName: string;
    role: string;
    suiteName: string;
    suiteFunction: string;
  };
  receiver: {
    agentName: string;
    role: string;
    suiteName: string;
    suiteFunction: string;
  };
  taskType: string;
  taskId: string;
  createdAt: string; // ISO datetime
  status: string;
}

export interface AgenticTickerApiResponse {
  status: boolean;
  message: string;
  data: {
    items: ApiAgenticTickerEntry[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface TaskTypeReference {
  id: string;
  name: string;
  description: string;
}

export interface TaskTypeReferencesApiResponse {
  status: boolean;
  message: string;
  data: TaskTypeReference[];
}

// UI Types (for backward compatibility and UI display)
export interface AgenticTickerEntry {
  id: string;
  eventId: string;
  eventType: string; // Changed from union to string to support dynamic types
  owner: {
    name: string;
    image: string;
    role?: string;
    suiteName?: string;
    suiteFunction?: string;
  };
  receiver: {
    name: string;
    image?: string; // Optional for external clients
    isExternal: boolean;
    role?: string;
    suiteName?: string;
    suiteFunction?: string;
  };
  timestamp: Date;
  status:
    | 'QUEUED'
    | 'NOT_STARTED'
    | 'ESCALATED'
    | 'IN_PROGRESS'
    | 'COMPLETED'
    | 'FAILED'
    | string;
}

export interface FilterState {
  suites: string[];
  agents: string[];
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
    quickSelect:
      | 'Today'
      | 'This Week'
      | 'This Month'
      | 'This Year'
      | 'Custom'
      | null;
  };
  eventTypes: string[]; // Changed to support dynamic event types
  statuses: string[]; // Changed to support dynamic statuses
  searchQuery: string;
}

export interface FilterTag {
  id: string;
  type: 'suite' | 'agent' | 'date' | 'eventType' | 'status';
  label: string;
  value: string;
}

export interface AgenticTickerPageState {
  entries: AgenticTickerEntry[];
  filteredEntries: AgenticTickerEntry[];
  filters: FilterState;
  filterTags: FilterTag[];
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  emptyStateType: 'no-data' | 'no-results' | 'api-error' | null;
}

export interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  quickSelect: string | null;
  onDateChange: (startDate: Date | null, endDate: Date | null) => void;
  onQuickSelectChange: (option: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export interface MultiSelectDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  selectedItems: string[];
  options: { value: string; label: string; icon?: string }[];
  onItemToggle: (value: string) => void;
  placeholder: string;
  title: string;
}

export interface StatusPillProps {
  status: string; // Changed to support dynamic statuses
  className?: string;
}

export interface OwnerReceiverModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'owner' | 'receiver';
  data: {
    name: string;
    image?: string;
    isExternal?: boolean;
  };
}
