import {
  addMonths,
  addYears,
  endOfMonth,
  endOfWeek,
  endOfYear,
  isSameDay,
  startOfMonth,
  startOfWeek,
  startOfYear,
  subMonths,
  subYears,
} from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import React, { useState } from 'react';

import { useTimezone } from '@/context/TimezoneContext';
import { useMediaQuery } from '@/hooks/useMediaQuery';

import { Button } from './Button';
import { Calendar } from './Calendar';

interface DateRange {
  from?: Date;
  to?: Date;
}

interface DateRangePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (range: DateRange) => void;
  initialRange?: DateRange;
  anchorRef?: React.RefObject<HTMLElement>;
  dropdownPosition?: 'bottom-left' | 'bottom-right';
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  isOpen,
  onClose,
  onApply,
  initialRange,
  anchorRef,
  dropdownPosition = 'bottom-left',
}) => {
  const { formatUserTimestamp } = useTimezone();

  const getQuickSelectOption = (range?: DateRange) => {
    if (!range?.from || !range?.to) return 'set_up';

    const now = new Date();
    const { from, to } = range;

    // Check Today
    if (isSameDay(from, now) && isSameDay(to, now)) {
      return 'today';
    }

    // Check This Week
    const weekStart = startOfWeek(now, { weekStartsOn: 0 });
    const weekEnd = endOfWeek(now, { weekStartsOn: 0 });
    if (isSameDay(from, weekStart) && isSameDay(to, weekEnd)) {
      return 'this_week';
    }

    // Check This Month
    const monthStart = startOfMonth(now);
    const monthEnd = endOfMonth(now);
    if (isSameDay(from, monthStart) && isSameDay(to, monthEnd)) {
      return 'this_month';
    }

    // Check This Year
    const yearStart = startOfYear(now);
    const yearEnd = endOfYear(now);
    if (isSameDay(from, yearStart) && isSameDay(to, yearEnd)) {
      return 'this_year';
    }

    return 'set_up';
  };

  const [selectedRange, setSelectedRange] = useState<DateRange>(
    initialRange || { from: undefined, to: undefined }
  );
  const [quickSelectOption, setQuickSelectOption] = useState<string>(() =>
    getQuickSelectOption(initialRange)
  );
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const isNotDesktop = useMediaQuery('(max-width: 1024px)');
  const isNotMobile = useMediaQuery('(min-width: 600px)');
  const isNotTablet = useMediaQuery('(min-width: 768px)');
  const isNotLargeScreen = useMediaQuery('(min-width: 1024px)');
  const isTablet = useMediaQuery('(max-width: 768px)');
  const isDesktop = useMediaQuery('(min-width: 1440px)');
  const handleQuickSelect = (option: string) => {
    setQuickSelectOption(option);
    const now = new Date();
    let range: DateRange = { from: undefined, to: undefined };

    switch (option) {
      case 'today':
        range = { from: now, to: now };
        break;
      case 'this_week': {
        const weekStart = startOfWeek(now, { weekStartsOn: 0 }); // Sunday start
        const weekEnd = endOfWeek(now, { weekStartsOn: 0 });
        range = { from: weekStart, to: weekEnd };
        break;
      }
      case 'this_month': {
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        range = { from: monthStart, to: monthEnd };
        break;
      }
      case 'this_year': {
        const yearStart = startOfYear(now);
        const yearEnd = endOfYear(now);
        range = { from: yearStart, to: yearEnd };
        break;
      }
      case 'set_up':
        // Keep current selection
        break;
    }

    if (option !== 'set_up') {
      setSelectedRange(range);
    }
  };

  const handleApply = () => {
    onApply(selectedRange);
    onClose();
  };

  const handleCancel = () => {
    setSelectedRange(initialRange || { from: undefined, to: undefined });
    onClose();
  };

  const handlePreviousYear = () => {
    setCurrentMonth(subYears(currentMonth, 1));
  };

  const handleNextYear = () => {
    setCurrentMonth(addYears(currentMonth, 1));
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const quickSelectOptions = [
    { id: 'today', label: 'Today' },
    { id: 'this_week', label: 'This week' },
    { id: 'this_month', label: 'This month' },
    { id: 'this_year', label: 'This year' },
    { id: 'set_up', label: 'Custom' },
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40"
            onClick={handleCancel}
          />

          {/* Popup */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="fixed z-50"
            style={{
              top: anchorRef?.current
                ? `${anchorRef.current.getBoundingClientRect().bottom + 8}px`
                : '50%',
              left: anchorRef?.current
                ? dropdownPosition === 'bottom-right'
                  ? `${anchorRef.current.getBoundingClientRect().right - 799}px`
                  : `${anchorRef.current.getBoundingClientRect().left}px`
                : '50%',
              transform: !anchorRef?.current ? 'translate(-50%, -50%)' : 'none',
              right: !anchorRef?.current ? 'auto' : 'auto',
              width: (() => {
                switch (true) {
                  case isTablet:
                    return '300px';
                  // case isNotMobile:
                  //   return '400px';
                  // case isNotTablet:
                  //   return '500px';
                  // case isNotLargeScreen:
                  //   return '600px';
                  // case isNotDesktop:
                  //   return 'min(799px, calc(100vw - 32px))';
                  case isDesktop:
                    return '799px';
                  default:
                    return '600px';
                }
              })(),
              maxHeight: '362px',
            }}
          >
            <div
              className="rounded-lg bg-white p-0"
              style={{
                boxShadow: '0px 4px 32px 0px #8890A133',
              }}
              onClick={e => e.stopPropagation()}
            >
              {/* Main Content Area */}
              <div className="flex items-start xl:divide-x xl:divide-gray-200">
                {/* Quick Select Options - Left Panel */}
                <div className="hidden w-[239px] flex-shrink-0 2xl:block">
                  <div className="flex flex-col divide-y divide-gray-200">
                    {quickSelectOptions.map(option => (
                      <label
                        key={option.id}
                        className={`flex cursor-pointer items-center gap-3 rounded p-4 transition-colors ${
                          quickSelectOption === option.id
                            ? 'bg-gray-100'
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        <input
                          type="radio"
                          name="dateRange"
                          checked={quickSelectOption === option.id}
                          onChange={() => handleQuickSelect(option.id)}
                          className="h-5 w-5 border-gray-300 text-black focus:ring-2 focus:ring-black"
                        />
                        <span className="text-sm font-normal text-black">
                          {option.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Calendar - Center Panel */}
                <div className="flex w-full flex-1 flex-col ">
                  {/* Custom Calendar Navigation */}
                  <div className="flex w-full items-center border-b py-3 md:gap-16">
                    {/* First Month */}
                    <div className="flex-1 px-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={handlePreviousYear}
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronsLeft className="h-4 w-4" />
                          </button>
                          <button
                            onClick={handlePreviousMonth}
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </button>
                        </div>
                        <span className="text-[10px] font-medium text-blackOne">
                          {currentMonth.toLocaleDateString('en-US', {
                            month: 'long',
                            year: 'numeric',
                          })}
                        </span>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={handleNextMonth}
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronRight className="h-4 w-4" />
                          </button>
                          <button
                            onClick={handleNextYear}
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronsRight className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="hidden flex-1 px-4 lg:block">
                      {/* Second Month */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() =>
                              setCurrentMonth(
                                subYears(addMonths(currentMonth, 1), 0)
                              )
                            }
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronsLeft className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              setCurrentMonth(addMonths(currentMonth, 1))
                            }
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </button>
                        </div>
                        <span className="text-[10px] font-medium text-blackOne">
                          {addMonths(currentMonth, 1).toLocaleDateString(
                            'en-US',
                            { month: 'long', year: 'numeric' }
                          )}
                        </span>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() =>
                              setCurrentMonth(addMonths(currentMonth, 1))
                            }
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronRight className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              setCurrentMonth(
                                addYears(addMonths(currentMonth, 1), 1)
                              )
                            }
                            className="flex h-7 w-7 items-center justify-center rounded hover:bg-gray-100"
                          >
                            <ChevronsRight className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Calendar Component */}
                  {isNotDesktop ? (
                    <Calendar
                      mode="range"
                      selected={{
                        from: selectedRange.from,
                        to: selectedRange.to,
                      }}
                      onSelect={range => {
                        if (range) {
                          setSelectedRange({
                            from: range.from,
                            to: range.to,
                          });
                          setQuickSelectOption('set_up');
                        }
                      }}
                      month={currentMonth}
                      numberOfMonths={1}
                      showOutsideDays={false}
                      className="w-full py-2"
                      classNames={{
                        months: 'flex w-full flex-1 justify-center',
                        month: 'space-y-4',
                        caption: 'hidden',
                        caption_label: 'hidden',
                        nav: 'hidden',
                        nav_button: 'hidden',
                        nav_button_previous: 'hidden',
                        nav_button_next: 'hidden',
                        table: 'w-full border-collapse space-y-1',
                        head_row: 'flex',
                        head_cell:
                          'text-gray-500 rounded-md w-9 font-normal text-xs [&:first-child]:text-red-500 [&:last-child]:text-red-500',
                        row: 'flex w-full justify-center',
                        cell: 'h-9 w-9 text-center text-sm p-0 relative',
                        day: 'h-9 w-9 p-0 font-normal hover:bg-gray-100 rounded-md',
                        day_range_start: 'bg-black text-white hover:bg-black',
                        day_range_end: 'bg-black text-white hover:bg-black',
                        day_selected: 'bg-black text-white hover:bg-black',
                        day_today: 'bg-gray-100 text-gray-900',
                        day_outside: 'text-gray-400 opacity-50',
                        day_disabled: 'text-gray-400 opacity-50',
                        day_range_middle:
                          'aria-selected:bg-orange-200 aria-selected:text-black',
                        day_hidden: 'invisible',
                      }}
                      modifiers={{
                        sunday: date => date.getDay() === 0,
                      }}
                      modifiersClassNames={{
                        sunday: 'text-red-500',
                      }}
                    />
                  ) : (
                    <Calendar
                      mode="range"
                      selected={{
                        from: selectedRange.from,
                        to: selectedRange.to,
                      }}
                      onSelect={range => {
                        if (range) {
                          setSelectedRange({
                            from: range.from,
                            to: range.to,
                          });
                          setQuickSelectOption('set_up');
                        }
                      }}
                      month={currentMonth}
                      numberOfMonths={2}
                      showOutsideDays={false}
                      className="w-full p-0"
                      classNames={{
                        months: 'flex w-full flex-1 justify-center gap-8 ml-2',
                        month: 'space-y-4',
                        caption: 'hidden',
                        caption_label: 'hidden',
                        nav: 'hidden',
                        nav_button: 'hidden',
                        nav_button_previous: 'hidden',
                        nav_button_next: 'hidden',
                        table: 'w-full border-collapse space-y-1',
                        head_row: 'flex',
                        head_cell:
                          'text-gray-500 rounded-md w-9 font-normal text-[0.8rem] [&:first-child]:text-red-500 [&:last-child]:text-red-500',
                        row: 'flex w-full ',
                        cell: 'h-9 w-9 text-center text-sm p-0 relative',
                        day: 'h-9 w-9 p-0 font-normal hover:bg-gray-100 rounded-md',
                        day_range_start: 'bg-black text-white hover:bg-black',
                        day_range_end: 'bg-black text-white hover:bg-black',
                        day_selected: 'bg-black text-white hover:bg-black',
                        day_today: 'bg-gray-100 text-gray-900',
                        day_outside: 'text-gray-400 opacity-50',
                        day_disabled: 'text-gray-400 opacity-50',
                        day_range_middle:
                          'aria-selected:bg-orange-200 aria-selected:text-black',
                        day_hidden: 'invisible',
                      }}
                      modifiers={{
                        sunday: date => date.getDay() === 0,
                      }}
                      modifiersClassNames={{
                        sunday: 'text-red-500',
                      }}
                    />
                  )}
                  {/* Bottom Panel - Date Inputs and Actions */}
                  <div className="flex items-center justify-between border-t border-gray-200 p-4">
                    <div className="hidden items-center gap-2 md:flex">
                      <input
                        type="text"
                        value={
                          selectedRange.from
                            ? formatUserTimestamp(selectedRange.from, 'date')
                                .replace(/\s/g, '.')
                                .replace(/,/g, '')
                            : ''
                        }
                        readOnly
                        className="w-28 rounded-full border-2 border-[#BAB9B9ß] bg-white text-sm text-black focus:outline-none"
                        placeholder="MM.DD.YYYY"
                      />
                      <span className="mx-1 text-gray-500">-</span>
                      <input
                        type="text"
                        value={
                          selectedRange.to
                            ? formatUserTimestamp(selectedRange.to, 'date')
                                .replace(/\s/g, '.')
                                .replace(/,/g, '')
                            : ''
                        }
                        readOnly
                        className="w-28 rounded-full border-2 border-[#BAB9B9ß] bg-white text-sm text-black focus:outline-none"
                        placeholder="MM.DD.YYYY"
                      />
                    </div>

                    <div className="flex w-full items-center justify-between gap-3 md:justify-end">
                      <Button
                        onClick={handleCancel}
                        className="w-full rounded-full md:w-fit"
                        variant="outline"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleApply}
                        className="w-full rounded-full md:w-fit"
                        variant="default"
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default DateRangePicker;
