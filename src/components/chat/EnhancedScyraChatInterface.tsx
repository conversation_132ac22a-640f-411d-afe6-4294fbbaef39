import { ArrowDown, User } from 'lucide-react';
import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkBreaks from 'remark-breaks';
import remarkGfm from 'remark-gfm';

import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';

import { scyra } from '../../assets/images';
import { useGetUserProfile } from '../../hooks/useUserProfile';
import {
  EnhancedScyraChatInterfaceProps,
  FormField,
  ScyraMessage,
} from '../../types/businessStack';
import { UserBasicInfoPayload } from '../../types/user';
import { getAgentAvatar } from '../../utils/agentUtils';
import { TypingIndicator } from './TypingIndicator';

const ScyraMessageComponent = ({ message }: { message: ScyraMessage }) => {
  const isUser = message.sender === 'user';
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { formatUserTimestamp } = useTimezone();

  const agentAvatar = getAgentAvatar(activeAgent, userData);

  // Enhanced content processing to ensure proper markdown formatting
  const processedContent = React.useMemo(() => {
    // Support either `content` (internal) or `message` (external API) fields
    // Some sources may provide the text under `message` instead of `content`.
    const rawField = message as unknown as Record<string, unknown>;
    const raw = (
      typeof rawField.content === 'string'
        ? rawField.content
        : typeof rawField.message === 'string'
          ? rawField.message
          : ''
    ) as string;
    if (!raw) return '';

    let content = raw;

    // Convert Windows line endings first
    content = content.replace(/\r\n/g, '\n');

    // Convert literal "\\n" sequences (backslash + n) into real newlines
    content = content.replace(/\\n/g, '\n');

    // Ensure there's a blank line before list items so markdown parsers pick them up
    content = content.replace(/\n(\s*)(\d+\.\s)/g, '\n\n$2');
    content = content.replace(/\n(\s*)([-*+]\s)/g, '\n\n$2');

    // Auto-link plain URLs that are not already in markdown link format
    // Avoid wrapping URLs that are already part of markdown links
    content = content.replace(
      /(?<!\]\()(?<!\()(https?:\/\/[^\s)]+)(?!\))/g,
      '[$1]($1)'
    );

    // Auto-link plain email addresses that are not already in markdown link format
    content = content.replace(
      /(?<!\]\()\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b(?!\))/g,
      '[$1](mailto:$1)'
    );

    // Preserve intentional spacing - do not trim trailing newlines which may be meaningful
    return content;
  }, [message]);

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full shrink-0 items-center justify-center rounded-full bg-grayTwentySix">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="shrink-0 rounded-full bg-peachTwo">
            <img
              src={agentAvatar}
              alt={activeAgent ? `${activeAgent} Agent` : 'Agent'}
              className="h-10 w-10 flex-shrink-0 rounded-full object-cover p-1"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-xs text-gray-400 sm:text-sm">
            {formatUserTimestamp(message.timestamp, 'time')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-lg bg-gray-5 p-3 text-grayTwentyFour">
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]} // Enable GitHub Flavored Markdown and line breaks
            components={{
              // Paragraph handling with proper spacing and line breaks
              p: ({ children, ...props }) => (
                <p
                  className="mb-3 whitespace-pre-wrap text-sm leading-relaxed last:mb-0"
                  style={{ whiteSpace: 'pre-line' }}
                  {...props}
                >
                  {children}
                </p>
              ),

              // Strong/bold text
              strong: ({ children, ...props }) => (
                <strong className="text-sm font-bold text-darkGray" {...props}>
                  {children}
                </strong>
              ),

              // Emphasis/italic text
              em: ({ children, ...props }) => (
                <em className="text-sm italic text-gray-700" {...props}>
                  {children}
                </em>
              ),

              // Inline code
              code: ({ children, ...props }) => (
                <code
                  className="rounded bg-gray-100 px-1.5 py-0.5 font-mono text-sm text-gray-800"
                  {...props}
                >
                  {children}
                </code>
              ),

              // Code blocks
              pre: ({ children, ...props }) => (
                <pre
                  className="my-3 overflow-x-auto rounded-lg bg-gray-100 p-3 font-mono text-sm text-gray-800"
                  {...props}
                >
                  {children}
                </pre>
              ),

              // Unordered lists
              ul: ({ children, ...props }) => (
                <ul
                  className="my-2 ml-6 list-disc space-y-1 text-sm"
                  {...props}
                >
                  {children}
                </ul>
              ),

              // Ordered lists
              ol: ({ children, ...props }) => (
                <ol
                  className="my-2 ml-6 list-decimal space-y-1 text-sm"
                  {...props}
                >
                  {children}
                </ol>
              ),

              // List items
              li: ({ children, ...props }) => (
                <li
                  className="text-sm leading-relaxed text-grayTwentyFour"
                  {...props}
                >
                  {children}
                </li>
              ),

              // Links - auto-link URLs and email addresses
              a: ({ children, href, ...props }) => (
                <a
                  href={href}
                  className="text-sm text-blue-600 underline hover:text-blue-800"
                  target="_blank"
                  rel="noopener noreferrer"
                  {...props}
                >
                  {children}
                </a>
              ),

              // Line breaks - ensure proper spacing
              br: ({ ...props }) => (
                <br className="text-sm leading-6" {...props} />
              ),

              // Headings (in case they appear in messages)
              h1: ({ children, ...props }) => (
                <h1
                  className="mb-3 mt-4 font-bold text-darkGray sm:text-xl"
                  {...props}
                >
                  {children}
                </h1>
              ),
              h2: ({ children, ...props }) => (
                <h2
                  className="mb-2 mt-3 font-bold text-darkGray sm:text-lg"
                  {...props}
                >
                  {children}
                </h2>
              ),
              h3: ({ children, ...props }) => (
                <h3
                  className="mb-2 mt-3 text-base font-bold text-darkGray"
                  {...props}
                >
                  {children}
                </h3>
              ),

              // Blockquotes
              blockquote: ({ children, ...props }) => (
                <blockquote
                  className="my-3 border-l-4 border-gray-300 pl-4 text-sm italic text-gray-600"
                  {...props}
                >
                  {children}
                </blockquote>
              ),

              // Horizontal rule
              hr: ({ ...props }) => (
                <hr className="my-4 border-gray-300 text-sm" {...props} />
              ),
            }}
          >
            {processedContent}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

// Main EnhancedScyraChatInterface component (keep your existing implementation)
export const EnhancedScyraChatInterface = ({
  state,
  ChatInputComponent,
  groupedMessages,
  connectionFlow,
  originalSendMessage,
  infiniteScroll,
}: EnhancedScyraChatInterfaceProps) => {
  const { activeAgent } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [currentPlaceholder, setCurrentPlaceholder] = useState<string>('');
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);
  // Track if we've done the initial scroll to bottom (only once on mount)
  const hasInitialScrolledRef = useRef(false);

  const capitalizeAgentName = (key: string) => {
    if (!key) return 'Regis';
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  // Helper function to get date key for grouping messages
  const getDateKey = (timestamp: string | Date): string => {
    const date = new Date(timestamp);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Helper function to format date headers for chat
  const formatDateHeader = (dateKey: string): string => {
    const date = new Date(dateKey);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';

    // For other dates, use our unified format
    return formatUserTimestamp(date, 'date');
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Get current field for connection flow
  const getCurrentField = useCallback((): FormField | null => {
    if (
      !connectionFlow?.state.currentApp ||
      connectionFlow.state.fieldsToCollect.length === 0
    ) {
      return null;
    }
    return (
      connectionFlow.state.fieldsToCollect[
        connectionFlow.state.currentFieldIndex
      ] || null
    );
  }, [connectionFlow]);

  // Update placeholder based on connection flow state
  useEffect(() => {
    if (!connectionFlow) return;

    const currentField = getCurrentField();
    if (currentField) {
      setCurrentPlaceholder(
        currentField.placeholder || `Enter your ${currentField.label}`
      );
    } else {
      setCurrentPlaceholder('');
    }
  }, [connectionFlow, getCurrentField]);

  // Handle user input during connection flows
  const handleConnectionInput = useCallback(
    (input: string) => {
      if (!connectionFlow || !connectionFlow.state.currentApp) return false;

      const { state: flowState } = connectionFlow;

      if (
        flowState.step === 'collecting-preauth' ||
        flowState.step === 'collecting-form'
      ) {
        // Process the input through connection flow
        connectionFlow.handleUserInput(input);

        return true; // Indicates input was handled by connection flow
      }

      return false; // Input should be handled normally
    },
    [connectionFlow]
  );

  // Get local messages from connection flow (memoized to avoid changing ref every render)
  const localMessages = React.useMemo<ScyraMessage[]>(
    () => connectionFlow?.state.localMessages ?? [],
    [connectionFlow?.state.localMessages]
  );

  // Create enhanced grouped messages that include local messages
  const allGroupedMessages = React.useMemo(() => {
    // Start with the passed groupedMessages
    const grouped: Record<string, ScyraMessage[]> = { ...groupedMessages };

    // Add local messages to the appropriate date groups
    localMessages.forEach((message: ScyraMessage) => {
      const dateKey = getDateKey(message.timestamp);
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }

      // Check if message already exists to prevent duplicates
      const messageExists = grouped[dateKey].some(
        existingMsg => existingMsg.id === message.id
      );
      if (!messageExists) {
        grouped[dateKey].push(message);
      }
    });

    // Sort messages within each date group
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      );
    });

    return grouped;
  }, [groupedMessages, localMessages]);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = (event: Event) => {
      const target = event.target as HTMLDivElement;
      const atBottom =
        target.scrollHeight - target.scrollTop - target.clientHeight < 40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);

      // Handle infinite scroll for loading more chat history
      if (infiniteScroll) {
        const syntheticEvent = {
          currentTarget: target,
        } as React.UIEvent<HTMLDivElement>;
        infiniteScroll.handleScroll(syntheticEvent);
      }
    };

    container.addEventListener('scroll', handleScroll);
    // Trigger initial scroll check with a proper synthetic event
    const syntheticScrollEvent = new Event('scroll');
    Object.defineProperty(syntheticScrollEvent, 'target', {
      value: container,
      enumerable: true,
    });
    handleScroll(syntheticScrollEvent);

    return () => container.removeEventListener('scroll', handleScroll);
  }, [state.messages.length, localMessages.length, infiniteScroll]);

  // Set initial scroll position to bottom ONLY when messages first appear (initial mount)
  // This prevents interference with pagination when user scrolls to top to load more messages
  useLayoutEffect(() => {
    const container = messagesContainerRef.current;
    const totalMessages = state.messages.length + localMessages.length;

    // Only scroll to bottom if:
    // 1. We haven't done the initial scroll yet (hasInitialScrolledRef is false)
    // 2. There are messages to display (totalMessages > 0)
    // 3. We're not currently loading more messages via pagination (which would mean user scrolled up)
    if (
      !hasInitialScrolledRef.current &&
      container &&
      totalMessages > 0 &&
      !infiniteScroll?.isLoadingMore
    ) {
      // Immediately set scroll to bottom without animation
      container.scrollTop = container.scrollHeight;
      // Mark that we've done the initial scroll - this prevents it from running again
      hasInitialScrolledRef.current = true;
    }
  }, [
    state.messages.length,
    localMessages.length,
    infiniteScroll?.isLoadingMore,
  ]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom AND this is not the initial load and not loading more
    if (
      isUserAtBottom &&
      state.messages.length > 0 &&
      !infiniteScroll?.isLoadingMore
    ) {
      scrollToBottom();
    }
  }, [
    state.messages,
    localMessages,
    isUserAtBottom,
    infiniteScroll?.isLoadingMore,
  ]);

  // Handle loading state changes - only scroll if user is at bottom and not loading more
  useEffect(() => {
    if (
      !state.isLoading &&
      isUserAtBottom &&
      state.messages.length > 0 &&
      !infiniteScroll?.isLoadingMore
    ) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [
    state.isLoading,
    isUserAtBottom,
    state.messages.length,
    infiniteScroll?.isLoadingMore,
  ]);

  // Clear placeholder when connection flow resets
  useEffect(() => {
    if (connectionFlow?.state.step === 'idle') {
      setCurrentPlaceholder('');
    }
  }, [connectionFlow?.state.step]);

  // Enhanced ChatInputComponent with connection flow handling
  const EnhancedChatInput = React.useMemo(() => {
    if (!ChatInputComponent) return null;

    // Create a wrapper that handles the enhanced functionality
    interface ChatInputProps {
      placeholder?: string;
      onSendMessage: (msg: string) => void;
      disabled?: boolean;
    }

    const ConnectionAwareChatInput = () => {
      const handleSendMessage = (input: string) => {
        // Check if connection flow should handle this input
        const handledByConnection = handleConnectionInput(input);

        // If not handled by connection flow, call the original sendMessage
        if (!handledByConnection && originalSendMessage) {
          originalSendMessage(input);
        }
      };

      const Component =
        ChatInputComponent as React.ComponentType<ChatInputProps>;

      return (
        <Component
          placeholder={
            currentPlaceholder || "I'm here — whenever you're ready."
          }
          onSendMessage={handleSendMessage}
          disabled={connectionFlow?.state.isLoading || state.isLoading}
        />
      );
    };

    return <ConnectionAwareChatInput />;
  }, [
    ChatInputComponent,
    currentPlaceholder,
    handleConnectionInput,
    connectionFlow?.state.isLoading,
    state.isLoading,
    originalSendMessage,
  ]);

  return (
    <div className="relative flex h-full min-h-0 flex-1 flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="mt-2 min-h-0 flex-1 overflow-y-auto p-4"
        style={{ minHeight: 0 }}
      >
        {/* Loading indicator for loading more chat history*/}
        {infiniteScroll?.isLoadingMore && infiniteScroll?.hasNextPage && (
          <div className="flex justify-center py-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary"></div>
            </div>
          </div>
        )}

        {Object.entries(allGroupedMessages).map(([date, messages]) => (
          <div key={date}>
            <div className="mb-2 text-center text-xs font-semibold text-gray-400">
              {formatDateHeader(date)}
            </div>
            {messages.map(message => (
              // Try the HTML version first for better formatting control
              // <ScyraMessageComponentHTML key={message.id} message={message} />
              // Or use the markdown version:
              <ScyraMessageComponent key={message.id} message={message} />
            ))}
          </div>
        ))}

        {/* Typing Indicator */}
        {(state.isLoading || connectionFlow?.state.isLoading) && (
          <TypingIndicator
            agentImageSrc={scyra}
            agentName={capitalizeAgentName(activeAgent || 'Regis')}
            message={
              connectionFlow?.state.step === 'waiting-auth'
                ? `${capitalizeAgentName(activeAgent || 'Regis')} is waiting`
                : undefined
            }
          />
        )}
      </div>

      {/* Floating Down Arrow */}
      {showScrollToBottom && (
        <button
          className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
          onClick={scrollToBottom}
          aria-label="Scroll to latest message"
        >
          <ArrowDown className="h-6 w-6 text-primary" />
        </button>
      )}

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">{EnhancedChatInput}</div>
    </div>
  );
};
