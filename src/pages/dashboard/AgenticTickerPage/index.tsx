import { AnimatePresence, motion } from 'framer-motion';
import { Search } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { useDebounce } from '@/hooks/useDebounce';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';

import { Icons } from '../../../assets/icons/DashboardIcons';
import AppContainer from '../../../components/common/AppContainer';
import Pagination from '../../../components/common/Pagination';
import {
  useAgenticTickerQuery,
  useAgenticTickerRefresh,
} from '../../../hooks/useAgenticTickerQueries';
import {
  AgenticTickerEntry,
  FilterState,
  FilterTag,
} from '../../../types/agenticTicker';
import AgenticTickerTable from './components/AgenticTickerTable';
import AgenticTickerTableSkeleton from './components/AgenticTickerTableSkeleton';
import FilterControls from './components/FilterControls';
import FilterTags from './components/FilterTags';
import OwnerReceiverModal from './components/OwnerReceiverModal';

const AgenticTickerPage: React.FC = () => {
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const filterButtonRef = useRef<HTMLDivElement>(null);

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    suites: [],
    agents: [],
    dateRange: {
      startDate: null,
      endDate: null,
      quickSelect: null,
    },
    eventTypes: [],
    statuses: [],
    searchQuery: '',
  });

  const [showFilterOptions, setShowFilterOptions] = useState(false);
  const [filterTags, setFilterTags] = useState<FilterTag[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const [paginatedEntries, setPaginatedEntries] = useState<
    AgenticTickerEntry[]
  >([]);

  // Local search state for immediate UI feedback
  const [localSearchQuery, setLocalSearchQuery] = useState(filters.searchQuery);

  // Debounced search query with 2000ms delay
  const debouncedSearchQuery = useDebounce(localSearchQuery, 2000);

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  // React Query hooks for data fetching
  const { data, isLoading, error } = useAgenticTickerQuery({
    filters: {
      ...filters,
      searchQuery: debouncedSearchQuery,
    },
    currentPage,
    pageSize: itemsPerPage,
  });

  // Extract data from React Query response
  const entries = useMemo(() => data?.items || [], [data?.items]);
  const totalItems = data?.total || 0;

  const { refreshData } = useAgenticTickerRefresh();

  // Modal state
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    type: 'owner' | 'receiver';
    data: {
      name: string;
      image?: string;
      role?: string;
      suiteName?: string;
      suiteFunction?: string;
      isExternal?: boolean;
    };
  }>({
    isOpen: false,
    type: 'owner',
    data: { name: '', image: '', isExternal: false },
  });

  // Sync debounced search query with filters
  useEffect(() => {
    if (debouncedSearchQuery !== filters.searchQuery) {
      setFilters(prev => ({ ...prev, searchQuery: debouncedSearchQuery }));
      setCurrentPage(1); // Reset to first page when search changes
    }
  }, [debouncedSearchQuery, filters.searchQuery]);

  // Sync local search state when filters change externally (e.g., clearing filters)
  useEffect(() => {
    if (
      filters.searchQuery !== localSearchQuery &&
      filters.searchQuery !== debouncedSearchQuery
    ) {
      setLocalSearchQuery(filters.searchQuery);
    }

  }, [filters.searchQuery, localSearchQuery, debouncedSearchQuery]);
  // Update paginated entries when data changes
  useEffect(() => {
    setPaginatedEntries(Array.isArray(entries) ? entries : []);
  }, [entries]);

  // Calculate total pages based on API response
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Generate filter tags based on active filters
  useEffect(() => {
    const tags: FilterTag[] = [];

    // Suite tags
    // Get claimed agent suites for name resolution
    const claimedAgentSuites =
      userData?.userInfo?.tenant?.claimedAgentSuites ?? [];

    filters.suites.forEach(suiteId => {
      const suite = claimedAgentSuites.find(
        claimed => claimed?.suite?.agentSuiteKey === suiteId
      );
      const suiteName = suite?.suite?.agentSuiteName || suiteId;

      tags.push({
        id: `suite-${suiteId}`,
        type: 'suite',
        label: `Suite: ${suiteName}`,
        value: suiteId,
      });
    });

    // Agent tags
    filters.agents.forEach(agentId => {
      let agentName = agentId;
      for (const claimed of claimedAgentSuites) {
        const agent = claimed?.suite?.availableAgents?.find(
          agent => agent.agentKey === agentId
        );
        if (agent) {
          agentName = agent.agentName;
          break;
        }
      }
      
      tags.push({
      // Find agent across all suites

        id: `agent-${agentId}`,
        type: 'agent',
        label: `Agent: ${agentName}`,
        value: agentId,
      });
    });

    // Date range tag
    if (
      filters.dateRange.startDate ||
      filters.dateRange.endDate ||
      filters.dateRange.quickSelect
    ) {
      let dateLabel = 'Date: ';
      if (
        filters.dateRange.quickSelect &&
        filters.dateRange.quickSelect !== 'Custom'
      ) {
        dateLabel += filters.dateRange.quickSelect;
      } else if (filters.dateRange.startDate && filters.dateRange.endDate) {
        dateLabel += `${filters.dateRange.startDate.toLocaleDateString()} → ${filters.dateRange.endDate.toLocaleDateString()}`;
      } else if (filters.dateRange.startDate) {
        dateLabel += `From ${filters.dateRange.startDate.toLocaleDateString()}`;
      } else if (filters.dateRange.endDate) {
        dateLabel += `Until ${filters.dateRange.endDate.toLocaleDateString()}`;
      }

      tags.push({
        id: 'date-range',
        type: 'date',
        label: dateLabel,
        value: 'date-range',
      });
    }

    // Event type tags
    filters.eventTypes.forEach(eventType => {
      tags.push({
        id: `eventType-${eventType}`,
        type: 'eventType',
        label: `Type: ${eventType}`,
        value: eventType,
      });
    });

    // Status tags
    filters.statuses.forEach(status => {
      tags.push({
        id: `status-${status}`,
        type: 'status',
        label: `Status: ${status}`,
        value: status,
      });
    });

    // Search query tag
    if (filters.searchQuery.trim()) {
      tags.push({
        id: 'search',
        type: 'eventType', // Using eventType as a generic type
        label: `Search: "${filters.searchQuery}"`,
        value: filters.searchQuery,
      });
    }

    setFilterTags(tags);
  }, [filters, userData?.userInfo?.tenant?.claimedAgentSuites]);

  const handleFiltersChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    // Reset to first page when filters change
    setCurrentPage(1);
  };

  const handleReload = () => {
    // Manual reload using React Query's refresh functionality
    refreshData(
      {
        ...filters,
        searchQuery: debouncedSearchQuery,
      },
      currentPage,
      itemsPerPage
    );
  };

  const handleRemoveTag = (tagId: string) => {
    const tag = filterTags.find(t => t.id === tagId);
    if (!tag) return;

    const newFilters = { ...filters };

    switch (tag.type) {
      case 'suite':
        newFilters.suites = filters.suites.filter(id => id !== tag.value);
        break;
      case 'agent':
        newFilters.agents = filters.agents.filter(id => id !== tag.value);
        break;
      case 'date':
        newFilters.dateRange = {
          startDate: null,
          endDate: null,
          quickSelect: null,
        };
        break;
      case 'eventType':
        if (tagId === 'search') {
          newFilters.searchQuery = '';
        } else {
          newFilters.eventTypes = filters.eventTypes.filter(
            type => type !== tag.value
          );
        }
        break;
      case 'status':
        newFilters.statuses = filters.statuses.filter(
          status => status !== tag.value
        );
        break;
    }

    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleClearAllTags = () => {
    setFilterTags([]);
    setFilters({
      suites: [],
      agents: [],
      dateRange: {
        startDate: null,
        endDate: null,
        quickSelect: null,
      },
      eventTypes: [],
      statuses: [],
      searchQuery: '',
    });
    // Reset to first page when clearing filters
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleOwnerClick = (owner: {
    name: string;
    image: string;
    role?: string;
    suiteName?: string;
    suiteFunction?: string;
  }) => {
    setModalState({
      isOpen: true,
      type: 'owner',
      data: {
        name: owner.name,
        image: owner.image,
        role: owner.role,
        suiteName: owner.suiteName,
        suiteFunction: owner.suiteFunction,
        isExternal: false,
      },
    });
  };

  const handleReceiverClick = (receiver: {
    name: string;
    image?: string;
    isExternal: boolean;
    role?: string;
    suiteName?: string;
    suiteFunction?: string;
  }) => {
    setModalState({
      isOpen: true,
      type: 'receiver',
      data: {
        name: receiver.name,
        image: receiver.image,
        isExternal: receiver.isExternal,
        role: receiver.role,
        suiteName: receiver.suiteName,
        suiteFunction: receiver.suiteFunction,
      },
    });
  };

  const handleCloseModal = () => {
    setModalState(prev => ({ ...prev, isOpen: false }));
  };

  // Determine empty state type
  const getEmptyStateType = (): 'no-data' | 'no-results' | 'api-error' => {
    if (error) return 'api-error';
    if (entries.length === 0) {
      return 'no-results';
    }
    if (paginatedEntries.length === 0) {
      return 'no-data';
    }
    return 'api-error';
  };

  const handleRetry = () => {
    handleReload();
  };

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <AppContainer
        isPadding={false}
        className="space-y-6 p-0 sm:p-8 lg:space-y-8"
      >
        {/* Header */}
        <div className="mb-6">
          <div className="flex w-full items-center justify-between gap-4">
            {/* Title */}
            <div className="flex flex-shrink-0 items-center gap-3">
              <Icons.Ticker className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold text-blackFour">
                Agentic Ticker (Live Feed)
              </h1>
            </div>

            {/* Search Bar and Filter Button */}
            <div className="flex items-center gap-4">
              <div className="relative h-11 min-w-0 max-w-2xl flex-1">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search"
                  value={localSearchQuery}
                  onChange={e => setLocalSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-200 py-3 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>

              <div ref={filterButtonRef}>
                <button
                  onClick={() => setShowFilterOptions(!showFilterOptions)}
                  className="flex h-[44px] items-center gap-3 rounded-[10px] border-2 border-primary bg-[#FDF7F6] px-4 text-primary transition-colors hover:bg-primary hover:text-white"
                >
                  <span className="font-spartan text-sm font-semibold">
                    Filter
                  </span>
                  <Icons.DownloadCloud className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <AnimatePresence>
          {showFilterOptions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="w-full"
            >
              <div className="px-4">
                <FilterControls
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onReload={handleReload}
                  isLoading={isLoading}
                  className=""
                />

                {/* Filter Tags */}
                <FilterTags
                  tags={filterTags}
                  onRemoveTag={handleRemoveTag}
                  onClearAll={handleClearAllTags}
                  className="mt-4"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agentic Ticker Table */}
        {isLoading && entries.length === 0 ? (
          <AgenticTickerTableSkeleton />
        ) : (
          <AgenticTickerTable
            entries={paginatedEntries}
            isLoading={false}
            onOwnerClick={handleOwnerClick}
            onReceiverClick={handleReceiverClick}
            emptyStateType={getEmptyStateType()}
            onRetry={handleRetry}
            className=""
          />
        )}

        {/* Pagination */}
        <div className="mt-6">
          {!isLoading && totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              className="mt-6"
            />
          )}
        </div>
      </AppContainer>
      {/* Owner/Receiver Modal */}
      <OwnerReceiverModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        type={modalState.type}
        data={modalState.data}
      />
    </DashboardWithChatLayout>
  );
};

export default AgenticTickerPage;
