import React from 'react';

import { useTimezone } from '@/context/TimezoneContext';

interface AgenticTickerTableSkeletonProps {
  className?: string;
  rowCount?: number;
}

const AgenticTickerTableSkeleton: React.FC<AgenticTickerTableSkeletonProps> = ({
  className = '',
  rowCount = 15,
}) => {
  const { formatUserTimestamp } = useTimezone();

  return (
    <div className="relative w-full overflow-hidden">
      <div className={`overflow-hidden text-blackOne shadow-sm ${className}`}>
        {/* Table Header */}
        <div className="relative z-10 border-y border-grayNine bg-[#FBEFE7]">
          <div className="grid grid-cols-6 gap-4 px-6 py-4 font-medium">
            <div>Owner</div>
            <div>Event Type</div>
            <div>Event ID</div>
            <div className="-ml-6">{`
              Timestamp 
              (${formatUserTimestamp(Date(), 'time').split(' ')[2]})`}</div>
            <div className="ml-4">Receiver</div>
            <div className="ml-6">Status</div>
          </div>
        </div>

        {/* Skeleton Table Body */}
        <div className="bg-white backdrop-blur-sm">
          <div className="divide-y divide-grayFifteen border-b border-grayFifteen">
            {Array.from({ length: rowCount }, (_, index) => (
              <div
                key={`skeleton-row-${index}`}
                className={`grid grid-cols-6 gap-4 border-grayFifteen px-4 py-3 ${
                  index % 2 === 0 ? 'bg-[#FBEFE7]' : ''
                }`}
              >
                {/* Owner Skeleton */}
                <div className="flex items-center">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 animate-pulse rounded-lg bg-gray-200"></div>
                    <div className="h-4 w-16 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>

                {/* Event Type Skeleton */}
                <div className="flex items-center">
                  <div className="flex items-center gap-2">
                    <div className="h-6 w-6 animate-pulse rounded bg-gray-200"></div>
                    <div className="h-4 w-20 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>

                {/* Event ID Skeleton */}
                <div className="flex items-center">
                  <div className="h-4 w-24 animate-pulse rounded bg-gray-200"></div>
                </div>

                {/* Timestamp Skeleton */}
                <div className="flex items-center -ml-6">
                  <div className="space-y-1">
                    <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
                    <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>

                {/* Receiver Skeleton */}
                <div className="flex items-center ml-4">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 animate-pulse rounded-lg bg-gray-200"></div>
                    <div className="h-4 w-16 animate-pulse rounded bg-gray-200"></div>
                  </div>
                </div>

                {/* Status Skeleton */}
                <div className="flex items-center ml-6">
                  <div className="h-6 w-20 animate-pulse rounded-full bg-gray-200"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgenticTickerTableSkeleton;
