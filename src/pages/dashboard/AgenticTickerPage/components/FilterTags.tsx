import { X } from 'lucide-react';
import React from 'react';

import { FilterTag } from '../../../../types/agenticTicker';

interface FilterTagsProps {
  tags: FilterTag[];
  onRemoveTag: (tagId: string) => void;
  onClearAll: () => void;
  className?: string;
}

const FilterTags: React.FC<FilterTagsProps> = ({
  tags,
  onRemoveTag,
  onClearAll,
  className = '',
}) => {
  if (tags.length === 0) return null;

  const renderTagLabel = (tag: FilterTag) => {
    // For date range tags, keep the original styling
    if (tag.type === 'date') {
      return (
        <span className="font-medium capitalize text-subText">{tag.label}</span>
      );
    }

    // For other tags, split the label and style the control name and value differently
    const colonIndex = tag.label.indexOf(':');
    if (colonIndex === -1) {
      return (
        <span className="font-medium capitalize text-subText">{tag.label}</span>
      );
    }

    const controlName = tag.label.substring(0, colonIndex + 1); // e.g., "Type:"
    const controlValue = tag.label.substring(colonIndex + 1).trim(); // e.g., "AGENT_TASK"

    return (
      <span className="font-medium capitalize">
        <span className="text-primary">{controlName}</span>{' '}
        <span className="text-subText">{controlValue}</span>
      </span>
    );
  };

  return (
    <div className={`z-20 flex flex-wrap items-center gap-2 ${className}`}>
      {tags.map(tag => (
        <div
          key={tag.id}
          className="border-grayTen/20 flex items-center gap-1 rounded-md border px-3 py-1 text-sm"
        >
          {renderTagLabel(tag)}
          <button
            onClick={() => onRemoveTag(tag.id)}
            className="flex items-center justify-center rounded-full p-0.5 text-subText hover:bg-primary/20"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      ))}

      {tags.length > 1 && (
        <button
          onClick={onClearAll}
          className="text-sm text-gray-500 underline hover:text-gray-700"
        >
          Clear all
        </button>
      )}
    </div>
  );
};

export default FilterTags;
