import { ChevronDown, ChevronUp, RotateCw } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import DateRangePicker from '@/components/ui/DateRangePicker';
import { statusOptions } from '@/data/constants';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { fetchTaskTypeReferences } from '@/services/agenticTickerService';
import { TaskTypeReference } from '@/types/agenticTicker';
import { UserBasicInfoPayload } from '@/types/user';

import { FilterState } from '../../../../types/agenticTicker';
import MultiSelectDropdown from './MultiSelectDropdown';

interface FilterControlsProps {
  filters: FilterState;
  onFiltersChange: (filters: Partial<FilterState>) => void;
  onReload: () => void;
  isLoading?: boolean;
  className?: string;
}

const FilterControls: React.FC<FilterControlsProps> = ({
  filters,
  onFiltersChange,
  onReload,
  isLoading = false,
  className = '',
}) => {
  // const navigate = useNavigate(); // Unused for now
  const [currentSearchParams] = useSearchParams();

  const [eventTypeOptions, setEventTypeOptions] = useState<
    { value: string; label: string; icon?: string }[]
  >([]);
  const [_isLoadingEventTypes, setIsLoadingEventTypes] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const [currentDateRangeType, setCurrentDateRangeType] = useState<string>('');

  // Refs for click outside handling
  const suitesRef = useRef<HTMLDivElement>(null);
  const agentsRef = useRef<HTMLDivElement>(null);
  const dateRef = useRef<HTMLDivElement>(null);
  const eventTypeRef = useRef<HTMLDivElement>(null);
  const statusRef = useRef<HTMLDivElement>(null);

  // Click outside handlers
  useOnClickOutside(suitesRef, () => {
    if (openDropdown === 'suites') setOpenDropdown(null);
  });
  useOnClickOutside(agentsRef, () => {
    if (openDropdown === 'agents') setOpenDropdown(null);
  });
  useOnClickOutside(dateRef, () => {
    if (openDropdown === 'date') setOpenDropdown(null);
  });
  useOnClickOutside(eventTypeRef, () => {
    if (openDropdown === 'eventType') setOpenDropdown(null);
  });
  useOnClickOutside(statusRef, () => {
    if (openDropdown === 'status') setOpenDropdown(null);
  });

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  const claimedAgentSuites = useMemo(
    () => userData?.userInfo?.tenant?.claimedAgentSuites ?? [],
    [userData]
  );

  const suiteOptions = useMemo(
    () =>
      claimedAgentSuites
        .map(suite => ({
          id: suite?.suite?.agentSuiteKey ?? '',
          name: suite?.suite?.agentSuiteName ?? '',
          icon: suite?.suite?.avatar ?? '',
          displayOrder: suite?.suite?.displayOrder ?? 0,
          fileData: suite?.suite?.fileData ?? null,
        }))
        .sort((a, b) => a.displayOrder - b.displayOrder),
    [claimedAgentSuites]
  );

  // Get agents only from the currently selected suite
  // Function to get aggregated agent options from all selected suites
  const getAggregatedAgentOptions = () => {
    // Get all suites from claimedAgentSuites
    const allSuites = claimedAgentSuites
      .filter(suite => suite?.suite?.enabled) // Only include enabled suites
      .map(suite => ({
        agentSuiteKey: suite.suite.agentSuiteKey,
        agentSuiteName: suite.suite.agentSuiteName,
        displayOrder: suite.suite.displayOrder!,
        agents:
          suite.suite.availableAgents
            ?.filter(agent => agent.enabled) // Only include enabled agents
            .map(agent => ({
              id: agent.agentKey,
              name: agent.agentName,
              icon: agent.avatar,
              description: agent.description,
              displayOrder: agent.displayOrder!,
              agentSuiteKey: agent.agentSuiteKey,
              agentSuiteName: suite.suite.agentSuiteName,
            })) || [],
      }))
      .filter(suite => suite.agents.length > 0); // Only include suites with agents

    // Filter suites if filters.suites is specified
    const filteredSuites =
      filters.suites.length > 0
        ? allSuites.filter(suite =>
            filters.suites.includes(suite.agentSuiteKey)
          )
        : allSuites;

    // Sort suites by their displayOrder
    const sortedSuites = [...filteredSuites].sort(
      (a, b) => a.displayOrder - b.displayOrder
    );

    // Flatten agents, sorting each suite's agents by their displayOrder
    const aggregatedAgents = sortedSuites.flatMap(suite =>
      [...suite.agents]
        .sort((a, b) => a.displayOrder - b.displayOrder)
        .map(agent => ({
          id: agent.id,
          name: agent.name,
          icon: agent.icon,
          description: agent.description,
          suiteId: suite.agentSuiteKey,
          suiteName: suite.agentSuiteName,
        }))
    );

    return aggregatedAgents;
  };

  // Fetch event types from API on component mount
  useEffect(() => {
    const loadEventTypes = async () => {
      setIsLoadingEventTypes(true);
      try {
        const response = await fetchTaskTypeReferences();
        if (response.status && response.data) {
          // Map the data to options
          const options = response.data
            .filter((taskType: TaskTypeReference) => taskType.name) // Filter out items without name
            .map((taskType: TaskTypeReference) => ({
              value: taskType.name,
              label: taskType.name,
            }));

          // Sort alphabetically by label (case-insensitive)
          const sortedOptions = options.sort((a, b) =>
            a.label.localeCompare(b.label, undefined, { sensitivity: 'base' })
          );

          setEventTypeOptions(sortedOptions);
        } else {
          // Handle case where response doesn't have data
          setEventTypeOptions([]);
        }
      } catch (error) {
        console.error('Error fetching event types:', error);
        setEventTypeOptions([]);
      } finally {
        setIsLoadingEventTypes(false);
      }
    };

    loadEventTypes();
  }, []);

  const handleDropdownToggle = (dropdown: string) => {
    setOpenDropdown(openDropdown === dropdown ? null : dropdown);
  };

  const handleEventTypeToggle = (eventType: string) => {
    const eventTypeValue = eventType as 'Message' | 'Task' | 'Follow-Up';
    const newEventTypes = filters.eventTypes.includes(eventTypeValue)
      ? filters.eventTypes.filter(type => type !== eventType)
      : [...filters.eventTypes, eventTypeValue];

    // Check if all options are now selected, if so, reset to empty
    if (newEventTypes.length === eventTypeOptions.length) {
      onFiltersChange({ eventTypes: [] });
    } else {
      onFiltersChange({ eventTypes: newEventTypes });
    }
  };

  const handleStatusToggle = (status: string) => {
    const statusValue = status as
      | 'QUEUED'
      | 'NOT_STARTED'
      | 'ESCALATED'
      | 'IN_PROGRESS'
      | 'COMPLETED'
      | 'FAILED';
    const newStatuses = filters.statuses.includes(statusValue)
      ? filters.statuses.filter(s => s !== status)
      : [...filters.statuses, statusValue];

    // Check if all options are now selected, if so, reset to empty
    if (newStatuses.length === statusOptions.length) {
      onFiltersChange({ statuses: [] });
    } else {
      onFiltersChange({ statuses: newStatuses });
    }
  };

  // DateRangePicker helper functions
  const getCurrentDateRange = () => {
    return {
      from: filters.dateRange.startDate || undefined,
      to: filters.dateRange.endDate || undefined,
    };
  };

  const handleDateRangeApply = (range: { from?: Date; to?: Date }) => {
    onFiltersChange({
      dateRange: {
        ...filters.dateRange,
        startDate: range.from || null,
        endDate: range.to || null,
        quickSelect: 'Custom', // Set to custom when manually selecting dates
      },
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filter Controls */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Suites Multi-Select Dropdown */}
        <div ref={suitesRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'suites'}
            onToggle={() => handleDropdownToggle('suites')}
            selectedItems={filters.suites}
            options={suiteOptions.map(suite => ({
              value: suite.id,
              label: suite.name,
              avatar: suite.icon,
            }))}
            onItemToggle={(suiteId: string) => {
              const newSuites = filters.suites.includes(suiteId)
                ? filters.suites.filter(id => id !== suiteId)
                : [...filters.suites, suiteId];

              // If all suites are selected, reset to empty
              if (newSuites.length === suiteOptions.length) {
                onFiltersChange({ suites: [] });
              } else {
                onFiltersChange({ suites: newSuites });
              }
            }}
            placeholder="Suites"
            title="Suites"
            icon={<Icons.Agent className="w-4" />}
            className="min-w-32"
          />
        </div>

        {/* Agents Multi-Select Dropdown */}
        <div ref={agentsRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'agents'}
            onToggle={() => handleDropdownToggle('agents')}
            selectedItems={filters.agents}
            options={getAggregatedAgentOptions().map(agent => ({
              value: agent.id,
              label: agent.name,
              avatar: agent.icon,
            }))}
            onItemToggle={(agentId: string) => {
              const newAgents = filters.agents.includes(agentId)
                ? filters.agents.filter(id => id !== agentId)
                : [...filters.agents, agentId];

              // If all agents are selected, reset to empty
              const allAgentOptions = getAggregatedAgentOptions();
              if (newAgents.length === allAgentOptions.length) {
                onFiltersChange({ agents: [] });
              } else {
                onFiltersChange({ agents: newAgents });
              }
            }}
            placeholder="Agents"
            title="Agents"
            icon={<Icons.Agent className="w-4" />}
            className="min-w-32"
          />
        </div>

        {/* Date Range Picker */}
        <div ref={dateRef} className="relative">
          <button
            onClick={() => {
              setCurrentDateRangeType('date');
              setShowDateRangePicker(true);
            }}
            className="flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white px-3 py-2 text-sm font-normal text-blackOne hover:border-gray-300 focus:outline-none"
          >
            <div className="flex items-center gap-2">
              <Icons.DataDate className="w-5" />
              <span>Date</span>
            </div>
            {showDateRangePicker ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>

          <DateRangePicker
            key={`${currentSearchParams.get('from')}-${currentSearchParams.get('to')}-${currentDateRangeType}`}
            isOpen={showDateRangePicker}
            onClose={() => setShowDateRangePicker(false)}
            onApply={handleDateRangeApply}
            initialRange={getCurrentDateRange()}
            anchorRef={suitesRef}
          />
        </div>

        {/* Event Type Dropdown */}
        <div ref={eventTypeRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'eventType'}
            onToggle={() => handleDropdownToggle('eventType')}
            selectedItems={filters.eventTypes}
            options={eventTypeOptions}
            onItemToggle={handleEventTypeToggle}
            placeholder="Event Type"
            title="Event Types"
            icon={<Icons.OutlineTransaction className="w-5" />}
            className=""
          />
        </div>

        {/* Status Dropdown */}
        <div ref={statusRef} className="relative">
          <MultiSelectDropdown
            isOpen={openDropdown === 'status'}
            onToggle={() => handleDropdownToggle('status')}
            selectedItems={filters.statuses}
            options={statusOptions.map(status => ({
              value: status.value,
              label: status.label,
            }))}
            onItemToggle={handleStatusToggle}
            placeholder="Status"
            title="Status"
            icon={<Icons.StatusFilled className="w-5" />}
            className=""
          />
        </div>

        {/* Reload Button */}
        <button
          onClick={onReload}
          disabled={isLoading}
          className="z-10 flex h-[48px] w-[48px] items-center justify-center rounded-[10px] border border-gray-200 bg-[#23BD33] text-gray-500 hover:border-gray-300 hover:text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50"
          title="Reload feed"
        >
          <RotateCw
            className={`w-5 text-grayTen ${isLoading ? 'animate-spin' : ''}`}
          />
        </button>
      </div>
    </div>
  );
};

export default FilterControls;
