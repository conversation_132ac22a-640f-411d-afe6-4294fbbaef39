import clsx from 'clsx';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React from 'react';

import { Icons } from '../../../../assets/icons/DashboardIcons';

interface MultiSelectDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  selectedItems: string[];
  options: { value: string; label: string; icon?: string; avatar?: string }[];
  onItemToggle: (value: string) => void;
  placeholder: string;
  title: string;
  icon?: React.ReactNode;
  className?: string;
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  isOpen,
  onToggle,
  selectedItems,
  options,
  onItemToggle,
  placeholder,
  title,
  icon,
  className,
}) => {
  const getDisplayText = () => {
    if (selectedItems.length === 0) return placeholder;
    if (selectedItems.length === 1) {
      const option = options.find(opt => opt.value === selectedItems[0]);
      return option?.label || selectedItems[0];
    }
    return `${selectedItems.length} selected`;
  };

  const renderIcon = (iconName?: string) => {
    if (!iconName) return null;

    switch (iconName) {
      case 'Message':
        return <Icons.Message className="h-4 w-4 text-white" />;
      case 'Task':
        return <Icons.Task className="h-4 w-4 text-white" />;
      case 'Follow-Up':
        return <Icons.FollowUp className="h-4 w-4 text-white" />;
      default:
        return null;
    }
  };

  return (
    <div className={clsx('relative', className)}>
      <button
        onClick={onToggle}
        type="button"
        className="flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white px-3 py-2 text-sm font-normal text-blackOne hover:border-gray-300 focus:outline-none"
      >
        {icon}
        <span className="flex-1 text-left">{getDisplayText()}</span>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute left-0 top-full z-50 mt-1 w-full min-w-48 rounded-xl border border-gray-200 bg-white shadow-[0px_4px_12px_0px_#8890A133]"
        >
          <div className="p-2">
            <div className="max-h-60 overflow-y-auto">
              {options.map(option => (
                <button
                  key={option.value}
                  onClick={() => onItemToggle(option.value)}
                  className="flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm transition-colors hover:bg-gray-50"
                >
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(option.value)}
                    onChange={() => {}} // Handled by parent onClick
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  {option.avatar ? (
                    <div className="h-6 w-6 rounded bg-[#FFE0D1]">
                      <img
                        src={option.avatar}
                        alt={option.label}
                        className="h-6 w-6 rounded object-cover"
                        onError={e => {
                          // Hide image on error
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                  ) : option.icon ? (
                    <div className="flex h-6 w-6 items-center justify-center rounded bg-primary">
                      {renderIcon(option.icon)}
                    </div>
                  ) : null}
                  <span className="flex-1">{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
