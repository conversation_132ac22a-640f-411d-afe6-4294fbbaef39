import { AlertCircle, Zap } from 'lucide-react';
import React from 'react';

import { silentRocket } from '@/assets/images';

import { Icons } from '../../../../assets/icons/DashboardIcons';

interface EmptyStateProps {
  type: 'no-data' | 'no-results' | 'api-error';
  onRetry?: () => void;
  className?: string;
}

const EmptyStates: React.FC<EmptyStateProps> = ({
  type,
  onRetry,
  className = '',
}) => {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-data':
        return {
          icon: <Zap className="h-16 w-16 text-primary" />,
          title: 'Mission Ready',
          description:
            'Activate your first agent tasks to bring your mission console online.',
          subDescription: '',
          actionText: 'Get Started',
          showRetry: false,
        };

      case 'no-results':
        return {
          icon: <Icons.Ticker className="h-16 w-16 text-gray-400" />,
          title: 'Mission Silent',
          description: 'No agent interactions match your current filters.',
          subDescription: '',
          actionText: 'Clear Filters',
          showRetry: false,
        };

      case 'api-error':
        return {
          icon: <AlertCircle className="h-16 w-16 text-red-500" />,
          title: 'Mission Signal Lost',
          description: "The mission console isn't responding right now.",
          subDescription:
            "We'll re-establish connection and update you shortly.",
          actionText: 'Retry Connection',
          showRetry: true,
        };

      default:
        return {
          icon: <Icons.Ticker className="h-16 w-16 text-gray-400" />,
          title: 'No Data Available',
          description: 'There are no entries to display at this time.',
          subDescription: '',
          actionText: 'Refresh',
          showRetry: true,
        };
    }
  };

  const content = getEmptyStateContent();

  return (
    <div
      className={`flex justify-center bg-[#FBEFE7] px-6 py-16 text-center ${className}`}
    >
      <div className="flex w-2/3 flex-col items-center justify-center rounded-lg border border-[#FFE0D1] bg-[#FBF7F5] p-20 text-center">
        {/* Icon */}
        <div className="mb-6 h-24 w-24 rounded-lg bg-yellowOne p-4">
          <img
            src={silentRocket}
            alt="Mission Silent"
            className={`${content.title === 'Mission Signal Lost' && 'rotate-180'}`}
          />
        </div>

        {/* Title */}
        <h3 className="mb-3 text-lg font-semibold text-[#1A1A1A]">
          {content.title}
        </h3>

        {/* Description */}
        <div className="mb-8 max-w-md text-base leading-relaxed text-[#1A1A1A]">
          <div>{content.description}</div>
          <div>{content.subDescription}</div>
        </div>
      </div>
    </div>
  );
};

export default EmptyStates;
