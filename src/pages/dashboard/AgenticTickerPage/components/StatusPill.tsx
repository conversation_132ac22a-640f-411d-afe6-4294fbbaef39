import clsx from 'clsx';
import React from 'react';

interface StatusPillProps {
  status:
    | 'QUEUED'
    | 'NOT_STARTED'
    | 'ESCALATED'
    | 'IN_PROGRESS'
    | 'COMPLETED'
    | 'FAILED'
    | string;
  className?: string;
}

const StatusPill: React.FC<StatusPillProps> = ({ status, className }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'QUEUED':
      case 'NOT_STARTED':
        return 'bg-white border border-[#8890A1] text-[#8890A1]';
      case 'IN_PROGRESS':
        return 'bg-white border border-[#FBA320] text-[#FBA320]';
      case 'COMPLETED':
        return 'bg-[#EDF7ED] border border-[#3E8E58] text-[#3E8E58]';
      case 'ESCALATED':
      case 'FAILED':
        return 'bg-white border border-[#CE1111] text-[#CE1111]';
      default:
        return 'bg-white border border-gray-500 text-gray-500';
    }
  };

  const getStatusLabel = () => {
    switch (status) {
      case 'QUEUED':
        return 'Queued';
      case 'NOT_STARTED':
        return 'Not Started';
      case 'ESCALATED':
        return 'Escalated';
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'COMPLETED':
        return 'Completed';
      case 'FAILED':
        return 'Failed';
      default:
        return status;
    }
  };

  return (
    <span
      className={clsx(
        'inline-flex items-center rounded px-3 py-1 text-xs font-medium',
        getStatusStyles(),
        className
      )}
    >
      {getStatusLabel()}
    </span>
  );
};

export default StatusPill;
