import clsx from 'clsx';
import { motion } from 'framer-motion';
import { Calendar, ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  quickSelect: string | null;
  onDateChange: (startDate: Date | null, endDate: Date | null) => void;
  onQuickSelectChange: (option: string) => void;
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  quickSelect,
  onDateChange,
  onQuickSelectChange,
  isOpen,
  onToggle,
  className,
}) => {
  const [tempStartDate, setTempStartDate] = useState<string>(
    startDate ? startDate.toISOString().split('T')[0] : ''
  );
  const [tempEndDate, setTempEndDate] = useState<string>(
    endDate ? endDate.toISOString().split('T')[0] : ''
  );

  const quickSelectOptions = [
    'Today',
    'This Week',
    'This Month',
    'This Year',
    'Custom',
  ];

  const getDisplayText = () => {
    if (quickSelect && quickSelect !== 'Custom') {
      return quickSelect;
    }
    if (startDate && endDate) {
      return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
    }
    if (startDate) {
      return `From ${startDate.toLocaleDateString()}`;
    }
    if (endDate) {
      return `Until ${endDate.toLocaleDateString()}`;
    }
    return 'Select date range';
  };

  const handleQuickSelect = (option: string) => {
    onQuickSelectChange(option);

    if (option !== 'Custom') {
      const today = new Date();
      let start: Date;
      const end: Date = today;

      switch (option) {
        case 'Today':
          start = today;
          break;
        case 'This Week':
          start = new Date(today);
          start.setDate(today.getDate() - today.getDay());
          break;
        case 'This Month':
          start = new Date(today.getFullYear(), today.getMonth(), 1);
          break;
        case 'This Year':
          start = new Date(today.getFullYear(), 0, 1);
          break;
        default:
          return;
      }

      onDateChange(start, end);
      setTempStartDate(start.toISOString().split('T')[0]);
      setTempEndDate(end.toISOString().split('T')[0]);
    }
  };

  const handleApply = () => {
    const start = tempStartDate ? new Date(tempStartDate) : null;
    const end = tempEndDate ? new Date(tempEndDate) : null;
    onDateChange(start, end);
    onToggle();
  };

  const handleCancel = () => {
    setTempStartDate(startDate ? startDate.toISOString().split('T')[0] : '');
    setTempEndDate(endDate ? endDate.toISOString().split('T')[0] : '');
    onToggle();
  };

  return (
    <div className={clsx('relative', className)}>
      <button
        onClick={onToggle}
        type="button"
        className="flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white px-3 py-2 text-sm font-normal text-blackOne hover:border-gray-300 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
      >
        <Calendar className="h-4 w-4 text-gray-500" />
        <span className="flex-1 text-left">{getDisplayText()}</span>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute left-0 top-full z-50 mt-1 w-80 rounded-xl border border-gray-200 bg-white shadow-[0px_4px_12px_0px_#8890A133]"
        >
          <div className="p-4">
            {/* Quick Select Options */}
            <div className="mb-4">
              <div className="mb-2 text-xs font-medium uppercase text-gray-500">
                Quick Select
              </div>
              <div className="grid grid-cols-2 gap-2">
                {quickSelectOptions.map(option => (
                  <button
                    key={option}
                    onClick={() => handleQuickSelect(option)}
                    className={clsx(
                      'rounded-lg px-3 py-2 text-sm transition-colors',
                      quickSelect === option
                        ? 'bg-primary text-white'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>

            {/* Custom Date Inputs */}
            {quickSelect === 'Custom' && (
              <div className="mb-4">
                <div className="mb-2 text-xs font-medium uppercase text-gray-500">
                  Custom Range
                </div>
                <div className="space-y-3">
                  <div>
                    <label className="mb-1 block text-xs text-gray-600">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={tempStartDate}
                      onChange={e => setTempStartDate(e.target.value)}
                      className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-xs text-gray-600">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={tempEndDate}
                      onChange={e => setTempEndDate(e.target.value)}
                      className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={handleCancel}
                className="flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleApply}
                className="flex-1 rounded-lg bg-primary px-3 py-2 text-sm text-white hover:bg-primary/90"
              >
                Apply
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DateRangePicker;
