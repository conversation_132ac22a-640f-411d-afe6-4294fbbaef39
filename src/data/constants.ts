import {
  cubeIcon,
  kbIcon1,
  kbIcon2,
  kbIcon3,
  kbIcon4,
  kbIcon5,
  kbIcon6,
  rhombusIcon,
  starIcon,
} from '../assets/icons';
import {
  colin,
  colton,
  compton,
  liora,
  novaIq,
  operatorIq,
  regis,
  salesWing,
  scyra,
  setIq,
  seto,
  vesa,
} from '../assets/images';

// Status options for task logs, assignment logs and agentic ticker
export const statusOptions = [
  { id: 'queued', label: 'Queued', value: 'QUEUED' },
  { id: 'not-started', label: 'Not Started', value: 'NOT_STARTED' },
  { id: 'in-progress', label: 'In Progress', value: 'IN_PROGRESS' },
  { id: 'completed', label: 'Completed', value: 'COMPLETED' },
  { id: 'escalated', label: 'Escalated', value: 'ESCALATED' },
  { id: 'failed', label: 'Failed', value: 'FAILED' },
];

export interface AgentSuite {
  id: string;
  name: string;
  category: string;
  description: string;
  image: string;
  agents?: string[]; // IDs of agents in this suite
}

export interface IndividualAgent {
  id: string;
  name: string;
  role: string;
  description: string;
  category: string;
  image: string;
  suite?: string; // Suite ID this agent belongs to
  features: string[];
}

export const agentSuites: AgentSuite[] = [
  {
    id: 'set-iq',
    name: 'SetIQ',
    category: 'Collection Services AI Agents Suite',
    description:
      'Resolve accounts with precision, empathy, speed, and compliance.',
    image: setIq,
    agents: ['scyra', 'obed', 'compton', 'colton'],
  },
  {
    name: 'SalesWing',
    id: 'sales-wing',
    category: 'Sales Operations AI Agents Suite',
    description: 'Engage leads, schedule calls, and accelerate deal cycles.',
    image: salesWing,
  },
  {
    name: 'OperatorIQ',
    id: 'operator-iq',
    category: 'Project & Program Oversight AI Agents Suite',
    description:
      'Track progress, manage projects, and deliver results on time.',
    image: operatorIq,
  },
  {
    name: 'NovaIQ',
    id: 'nova-iq',
    category: 'Strategy & Innovation AI Agents Suite',
    description:
      'Drive strategy, spark innovation, and align teams effectively.',
    image: novaIq,
  },
  {
    name: 'Scyra',
    id: 'scyra',
    category: 'Strategy & Innovation AI Agents Suite',
    description:
      'Drive strategy, spark innovation, and align teams effectively.',
    image: scyra,
  },
  {
    name: 'Obed',
    id: 'obed',
    category: 'Sales Operations AI Agents Suite',
    description: 'Engage leads, schedule calls, and accelerate deal cycles.',
    image: liora,
  },
  {
    name: 'Compton',
    id: 'compton',
    category: 'Project & Program Oversight AI Agents Suite',
    description:
      'Track progress, manage projects, and deliver results on time.',
    image: compton,
  },
  {
    name: 'Colton',
    id: 'colton',
    category: 'Collections Coordination & Manager',
    description:
      'I ensure operations adhere to legal, ethical, and brand standards.',
    image: vesa,
  },
  {
    name: 'Regis',
    id: 'regis',
    category: 'Access Coordination & Account Manager',
    description: 'I handle account setup and resolve access issues.',
    image: regis,
  },
];

export const marketplaceAgents = [
  {
    name: 'Scyra',
    id: 'scyra',
    role: 'Smart Collections Communication',
    description: 'I message, resolve, and escalate when needed.',
    category: 'Collections Communication',
    image: scyra,
    features: [
      'Drives proactive, multi-channel outreach daily',
      'Reads and classifies all customer replies',
      'Learns, adapts, and improves message performance',
    ],
  },
  {
    name: 'Liora',
    id: 'liora',
    role: 'Legal Document Automation',
    description: 'I draft court-ready legal documents fast.',
    category: 'Legal Document Automation',
    image: liora,
    features: [
      'Generates court-ready Summons & Complaint Packages',
      'Surfaces high-impact recovery opportunities',
      'Continuously refines prioritization with predictive signals',
    ],
  },
  {
    name: 'Compton',
    id: 'compton',
    role: 'Collections Scoring',
    description: 'I score recovery likelihood and legal fit.',
    category: 'Resolution Scoring',
    image: compton,
    features: [
      'Maintains full history of all customer interactions',
      'Ensures availability across agents and campaigns',
      'Flags inconsistencies and supports investigations',
    ],
  },
  {
    name: 'Obed',
    id: 'obed',
    role: 'Voice Sentiment Analysis',
    description: 'I detect tone and trigger escalation.',
    category: 'Voice Sentiment Analysis',
    image: colin,
    features: [
      'Analyzes real-time voice tone for stress and intent',
      'Flags calls needing escalation to human agents',
      'Improves customer experience through empathy detection',
    ],
  },
  {
    name: 'Seto',
    id: 'seto',
    role: 'Settlement Planning Agent',
    description: 'I recommend the right settlement terms.',
    category: 'Settlement Planning',
    image: seto,
    features: [
      'Delivers tailored settlement offers with precision',
      'Tracks agent workflow and customer outcomes',
      'Aligns every step to strategy and compliance',
    ],
  },
  {
    name: 'Colton',
    id: 'colton',
    role: 'Collections Object Creation Agent',
    description: 'I detect tone and trigger escalation.',
    category: 'Collections Object Creation',
    image: colton,
    suite: 'set-iq',
    features: [
      'Advanced object creation and management',
      'Integrates with multiple data sources',
      'Provides real-time object status updates',
    ],
  },
];

export const agentCategories = [
  { id: 'COLLECTION_SERVICES', alias: 'Collections Services' },
  { id: 'UNDERWRITING_SERVICES', alias: 'Underwriting Services' },
  { id: 'ADMINISTRATIVE_SERVICES', alias: 'Administrative Services' },
  { id: 'SALES_OPERATIONS', alias: 'Sales Operations' },
  // { id: 'STRATEGY_AND_INNOVATION', alias: 'Strategy & Innovation' },
  // {
  //   id: 'PROJECT_AND_PROGRAM_MANAGEMENT',
  //   alias: 'Project & Program Management',
  // },
];

export const featureIcons = [starIcon, rhombusIcon, cubeIcon];
export const kbIcons = [kbIcon1, kbIcon2, kbIcon3, kbIcon4, kbIcon5, kbIcon6];
