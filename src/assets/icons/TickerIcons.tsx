export const TickerIcons = {
  ColleagueConsultation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.1862 3.02441C12.7866 3.02441 12.4077 3.11445 12.0684 3.2748C12.1979 3.44172 12.3144 3.6194 12.4158 3.80639C12.5152 3.98977 12.6001 4.18214 12.6694 4.38169C12.8075 4.77969 12.8828 5.20677 12.8828 5.65114C12.8828 6.09548 12.8075 6.52259 12.6694 6.92056C12.6002 7.12018 12.5152 7.31245 12.4158 7.49586C12.3144 7.68285 12.198 7.8605 12.0684 8.02745C12.4077 8.18777 12.7866 8.27787 13.1862 8.27787C14.6346 8.27794 15.8129 7.09958 15.8129 5.65121C15.8129 4.20274 14.6346 3.02441 13.1862 3.02441Z" fill="black"/>
      <path d="M13.186 9.53418C13.0306 9.53418 12.8756 9.54184 12.7217 9.55671C12.9614 9.74343 13.187 9.94747 13.3962 10.1673C13.6073 10.3892 13.8016 10.6271 13.9775 10.8788C14.5523 11.701 14.929 12.6713 15.0373 13.7202C15.0587 13.9267 15.0697 14.1361 15.0697 14.3481C15.0697 14.5682 15.0313 14.7795 14.9617 14.976H17.372C17.7187 14.976 17.9999 14.6948 17.9999 14.3481C17.9999 11.6937 15.8403 9.53418 13.186 9.53418Z" fill="black"/>
      <path d="M5.33102 6.92063C5.19293 6.52266 5.11766 6.09555 5.11766 5.65121C5.11766 5.20684 5.19293 4.77976 5.33102 4.38176C5.40032 4.18207 5.48522 3.98977 5.58464 3.80639C5.68603 3.6194 5.80243 3.44175 5.93205 3.2748C5.59269 3.11448 5.21381 3.02441 4.81423 3.02441C3.36583 3.02441 2.1875 4.20274 2.1875 5.65114C2.1875 7.09951 3.36583 8.27787 4.81423 8.27787C5.21381 8.27787 5.59269 8.1878 5.93205 8.02745C5.80247 7.8605 5.68603 7.68285 5.58464 7.49586C5.48522 7.31252 5.40028 7.12021 5.33102 6.92063Z" fill="black"/>
      <path d="M4.81394 9.53418C2.15954 9.53418 0 11.6937 0 14.3481C0 14.6949 0.281144 14.976 0.627889 14.976H3.0382C2.96855 14.7795 2.93023 14.5682 2.93023 14.3481C2.93023 14.1361 2.94124 13.9267 2.96254 13.7202C3.07082 12.6713 3.44759 11.701 4.02236 10.8788C4.19832 10.6271 4.39252 10.3892 4.60363 10.1673C4.81281 9.94747 5.03841 9.74343 5.27817 9.55671C5.12426 9.54184 4.96933 9.53418 4.81394 9.53418Z" fill="black"/>
      <path d="M11.4394 4.67907C11.3512 4.45861 11.2344 4.25263 11.0928 4.0662C10.6127 3.43381 9.85333 3.02441 8.99981 3.02441C8.14629 3.02441 7.38692 3.43381 6.90679 4.06627C6.76525 4.2527 6.64846 4.45868 6.56025 4.67914C6.43998 4.98005 6.37305 5.30784 6.37305 5.65118C6.37305 5.99448 6.43998 6.32227 6.56025 6.62318C6.64846 6.84364 6.76525 7.04962 6.90679 7.23605C7.38692 7.86844 8.14629 8.27787 8.99981 8.27787C9.85333 8.27787 10.6127 7.86844 11.0928 7.23598C11.2344 7.04955 11.3512 6.84357 11.4394 6.62311C11.5596 6.3222 11.6266 5.99444 11.6266 5.65111C11.6266 5.30777 11.5596 4.97998 11.4394 4.67907Z" fill="black"/>
      <path d="M13.7734 13.7193C13.6289 12.6154 13.1089 11.628 12.3463 10.8897C12.1657 10.7149 11.9716 10.554 11.7657 10.409C11.5532 10.2594 11.3282 10.1263 11.0925 10.0122C10.6392 9.79257 10.1466 9.64204 9.62831 9.57422V13.5101C9.62831 13.8568 9.34717 14.1379 9.00042 14.1379C8.65364 14.1379 8.37253 13.8568 8.37253 13.5101V9.57422C7.85423 9.64204 7.36162 9.79257 6.90835 10.0122C6.67263 10.1264 6.44763 10.2594 6.23514 10.409C6.02916 10.5541 5.83507 10.7149 5.65454 10.8897C4.89183 11.6279 4.37187 12.6154 4.22741 13.7193C4.20055 13.9249 4.18652 14.1344 4.18652 14.3472C4.18652 14.694 4.46767 14.9751 4.81441 14.9751H13.1865C13.5333 14.9751 13.8144 14.6939 13.8144 14.3472C13.8144 14.1344 13.8003 13.9249 13.7734 13.7193Z" fill="black"/>
    </svg>
  ),
  OutcomeReporting: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M0.845679 0.565714C1.20701 0.203934 1.69722 0.000450134 2.20854 0L9.39182 0C9.90314 0.000450134 10.3933 0.203934 10.7547 0.565714L15.1441 4.95514C15.5059 5.31647 15.7094 5.80668 15.7098 6.318V16.0714C15.7098 16.5829 15.5066 17.0735 15.145 17.4351C14.7833 17.7968 14.2927 18 13.7812 18H2.20982C1.69833 18 1.20779 17.7968 0.846115 17.4351C0.484438 17.0735 0.28125 16.5829 0.28125 16.0714V1.92857C0.28125 1.41686 0.483107 0.925714 0.845679 0.565714ZM6.85254 5.82043C6.95578 5.89398 7.04351 5.98716 7.11071 6.09465C7.17791 6.20214 7.22325 6.32182 7.24415 6.44685C7.26505 6.57188 7.26109 6.6998 7.2325 6.8233C7.20391 6.9468 7.15125 7.06344 7.07754 7.16657L5.28139 9.68143C5.19994 9.79522 5.09471 9.88992 4.97299 9.95895C4.85127 10.028 4.71598 10.0697 4.57652 10.0812C4.43706 10.0927 4.29676 10.0737 4.16538 10.0255C4.03399 9.97738 3.91466 9.9012 3.81568 9.80229L2.73825 8.72486C2.56792 8.54206 2.47519 8.30029 2.4796 8.05047C2.484 7.80066 2.58521 7.5623 2.76188 7.38563C2.93855 7.20895 3.17691 7.10775 3.42672 7.10335C3.67654 7.09894 3.91831 7.19167 4.10111 7.362L4.37368 7.63329L5.50768 6.04671C5.6562 5.83856 5.88132 5.69792 6.13352 5.65573C6.38572 5.61354 6.64435 5.67196 6.85254 5.82043ZM8.07396 8.19514C8.07396 7.9394 8.17556 7.69413 8.3564 7.51329C8.53724 7.33245 8.78251 7.23086 9.03825 7.23086H12.2525C12.5083 7.23086 12.7536 7.33245 12.9344 7.51329C13.1152 7.69413 13.2168 7.9394 13.2168 8.19514C13.2168 8.45089 13.1152 8.69616 12.9344 8.877C12.7536 9.05783 12.5083 9.15943 12.2525 9.15943H9.03825C8.78251 9.15943 8.53724 9.05783 8.3564 8.877C8.17556 8.69616 8.07396 8.45089 8.07396 8.19514ZM8.07396 13.2981C8.07396 13.0424 8.17556 12.7971 8.3564 12.6163C8.53724 12.4355 8.78251 12.3339 9.03825 12.3339H12.2525C12.5083 12.3339 12.7536 12.4355 12.9344 12.6163C13.1152 12.7971 13.2168 13.0424 13.2168 13.2981C13.2168 13.5539 13.1152 13.7992 12.9344 13.98C12.7536 14.1608 12.5083 14.2624 12.2525 14.2624H9.03825C8.78251 14.2624 8.53724 14.1608 8.3564 13.98C8.17556 13.7992 8.07396 13.5539 8.07396 13.2981ZM7.07754 12.3493C7.15468 12.2465 7.2105 12.1293 7.24171 12.0046C7.27292 11.88 7.27889 11.7503 7.25927 11.6233C7.23966 11.4963 7.19486 11.3745 7.12749 11.265C7.06013 11.1556 6.97157 11.0607 6.86701 10.986C6.76245 10.9112 6.64401 10.8582 6.51865 10.8299C6.39329 10.8016 6.26353 10.7986 6.13701 10.8212C6.01048 10.8437 5.88975 10.8914 5.78189 10.9612C5.67404 11.0311 5.58125 11.1219 5.50896 11.2281L4.37368 12.816L4.10111 12.5434C3.91831 12.3731 3.67654 12.2804 3.42672 12.2848C3.17691 12.2892 2.93855 12.3904 2.76188 12.5671C2.58521 12.7437 2.484 12.9821 2.4796 13.2319C2.47519 13.4817 2.56792 13.7235 2.73825 13.9063L3.81568 14.9837C3.91466 15.0826 4.03399 15.1588 4.16538 15.207C4.29676 15.2551 4.43706 15.2741 4.57652 15.2626C4.71598 15.2511 4.85127 15.2094 4.97299 15.1404C5.09471 15.0713 5.19994 14.9766 5.28139 14.8629L7.07754 12.3493Z" fill="#E6CF05"/>
    </svg>
  ),
  InformationVerification: () => (
    <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_19828_21401)">
      <path fillRule="evenodd" clipRule="evenodd" d="M9.5578 0.000130981C10.2027 -0.000857007 10.7749 -0.00173097 11.3035 0.217213C11.8321 0.436156 12.2362 0.841459 12.6914 1.29815C13.5935 2.20302 14.4971 3.10653 15.4019 4.00863C15.8586 4.46392 16.264 4.86798 16.4828 5.39656C16.7018 5.92514 16.7009 6.49745 16.6999 7.14231C16.6963 9.53241 16.6998 11.9225 16.6998 14.3127C16.6999 15.1548 16.6999 15.8804 16.6218 16.4614C16.538 17.0842 16.3493 17.6811 15.8651 18.1653C15.3809 18.6495 14.784 18.8383 14.1613 18.922C13.5801 19.0001 12.8545 19.0001 12.0124 19H6.18743C5.34527 19.0001 4.61965 19.0001 4.03857 18.922C3.41584 18.8383 2.81892 18.6495 2.33474 18.1653C1.85057 17.6811 1.66177 17.0842 1.57804 16.4614C1.49992 15.8804 1.49995 15.1548 1.5 14.3127L1.50001 4.75021C1.50001 4.72929 1.5 4.70844 1.5 4.68768C1.49995 3.84552 1.49992 3.11991 1.57804 2.53883C1.66177 1.9161 1.85057 1.31918 2.33475 0.835009C2.81892 0.350829 3.41584 0.162028 4.03857 0.0783055C4.61964 0.000178527 5.34526 0.000216547 6.18741 0.000264046C7.31089 0.000330545 8.43434 0.00185996 9.5578 0.000130981ZM12.6216 8.82838C12.9926 9.19935 12.9926 9.80088 12.6216 10.1719L9.22331 13.5701C8.63052 14.1629 7.66932 14.1629 7.07651 13.5701L5.5782 12.0718C5.20721 11.7009 5.20721 11.0993 5.5782 10.7284C5.94919 10.3574 6.55069 10.3574 6.92169 10.7284L8.14992 11.9566L11.2781 8.82838C11.6491 8.45741 12.2506 8.45741 12.6216 8.82838Z" fill="#456E49"/>
      </g>
      <defs>
      <clipPath id="clip0_19828_21401">
      <rect width="18" height="19" fill="white"/>
      </clipPath>
      </defs>
    </svg>
  ),
  CaseEvaluation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="3" fill="#554455"/>
      <path d="M5.90918 3.53418H11.5034V4.69161H5.90918V3.53418ZM5.90918 6.81372H8.55172V7.97115H5.90918V6.81372ZM5.90918 10.0932H8.55172V11.2507H5.90918V10.0932ZM5.90918 13.3888H7.03445V14.5141H5.90918V13.3888ZM7.82175 13.3888H8.94704V14.5141H7.82175V13.3888ZM9.73433 13.3888H10.8596V14.5141H9.73433V13.3888ZM11.6469 13.3888H12.7722V14.5141H11.6469V13.3888Z" fill="white"/>
      <path d="M15.7237 15.0117L14.2871 12.7685C14.8404 12.309 15.2253 11.6848 15.3817 10.971C15.5677 10.1237 15.4122 9.25473 14.9441 8.52433C14.6991 8.1417 14.3825 7.82304 14.016 7.57771V3.14364C14.016 2.51776 13.5067 2.00849 12.8824 2.00849L3.75698 1.96506C3.75623 1.96506 3.75561 1.96484 3.75486 1.96484C3.75417 1.96484 3.7536 1.96506 3.75291 1.96506L3.70855 1.96484L3.70853 1.96987C2.73902 1.99713 2.22949 2.82988 2.22949 3.65703V4.02045H4.55336L4.55127 15.0072C4.55127 15.6331 5.06038 16.1423 5.68626 16.1423H12.8807C13.5067 16.1423 14.016 15.6331 14.016 15.0072V14.1435L14.9069 15.5347L15.7237 15.0117ZM14.3322 8.91613V8.91631C14.6953 9.48327 14.816 10.1576 14.6719 10.8154C14.5285 11.4697 14.1396 12.0292 13.5774 12.3924L13.4705 12.4544C13.1082 12.6641 12.7158 12.7738 12.3249 12.7924C12.3142 12.7929 12.3034 12.793 12.2926 12.7934C12.2285 12.7956 12.1644 12.7953 12.1006 12.7926C12.0748 12.7915 12.0491 12.7892 12.0233 12.7872C11.9739 12.7836 11.9245 12.7793 11.8755 12.7728C11.8328 12.767 11.7902 12.7591 11.7476 12.7511C11.7135 12.7448 11.6793 12.7391 11.6455 12.7313C11.5943 12.7196 11.5442 12.7051 11.4943 12.6904C11.4667 12.6823 11.4387 12.6753 11.4114 12.6662C11.3627 12.6501 11.3154 12.6308 11.2681 12.6119C11.2398 12.6005 11.2111 12.5906 11.1833 12.5783C11.1416 12.5598 11.1015 12.5382 11.061 12.5176C11.0292 12.5013 10.9968 12.4866 10.9656 12.4689C10.9328 12.4504 10.9018 12.429 10.8698 12.409C10.8333 12.3861 10.7961 12.3645 10.7607 12.3396C10.7378 12.3235 10.7165 12.3051 10.6941 12.2882C10.6522 12.2565 10.6097 12.2256 10.5695 12.1912C10.5586 12.1818 10.5489 12.1711 10.5381 12.1616C10.3684 12.0108 10.2153 11.8357 10.0869 11.6351C10.0408 11.5631 9.99843 11.4889 9.95986 11.4127C9.95522 11.4036 9.95183 11.394 9.9473 11.3849C9.46716 10.4074 9.67637 9.2329 10.4405 8.48033C10.4442 8.47668 10.4474 8.47263 10.4511 8.46899C10.5004 8.42112 10.5533 8.37616 10.607 8.33194C10.619 8.32202 10.6299 8.31102 10.6422 8.3013C10.7089 8.24838 10.7782 8.19886 10.8499 8.15293C10.9495 8.08932 11.0534 8.03269 11.1608 7.98344C11.2203 7.95617 11.282 7.93675 11.3428 7.91437C11.3908 7.89667 11.4377 7.87549 11.4865 7.8608C11.5526 7.84097 11.6201 7.82891 11.6873 7.81464C11.7333 7.80485 11.7786 7.79182 11.8251 7.78467C11.8884 7.77493 11.9523 7.77236 12.016 7.76748C12.0683 7.76351 12.1203 7.75696 12.1729 7.75629C12.2783 7.75487 12.3838 7.7589 12.4888 7.7708C12.4935 7.77134 12.4983 7.77252 12.503 7.77308C12.6051 7.78527 12.7068 7.80422 12.8074 7.82902C12.824 7.83309 12.8402 7.83802 12.8567 7.84239C12.9549 7.86872 13.0523 7.89973 13.1478 7.93819C13.1514 7.93965 13.1551 7.94089 13.1587 7.94237C13.2661 7.98616 13.3704 8.03718 13.4708 8.09508L13.5071 8.116C13.8363 8.31336 14.1192 8.58351 14.3322 8.91613ZM3.0117 3.29358C3.10185 2.99643 3.31585 2.69313 3.75232 2.69189L3.7577 2.69191C4.19399 2.69328 4.40791 2.99651 4.49803 3.29358H3.0117ZM13.2892 15.0072C13.2892 15.2324 13.106 15.4155 12.8807 15.4155H5.68626C5.46125 15.4155 5.27812 15.2324 5.27812 15.0072L5.2802 4.02043H5.28024V3.65701C5.28024 3.3195 5.19191 2.9831 5.02611 2.69796L12.8807 2.73534C13.1061 2.73534 13.2892 2.91847 13.2892 3.14366V7.21405C13.2841 7.21225 13.2789 7.21135 13.2738 7.20956C13.1778 7.17645 13.0797 7.14814 12.9799 7.1238C12.9612 7.11921 12.9429 7.11299 12.9242 7.10872C12.9176 7.10723 12.9114 7.10492 12.9048 7.10348C12.7965 7.07982 12.687 7.06163 12.5768 7.04899C12.5627 7.04735 12.5484 7.04747 12.5342 7.04603C12.4325 7.0356 12.331 7.02946 12.2296 7.02863C12.1987 7.02841 12.1679 7.03066 12.1369 7.03133C12.0514 7.03308 11.9661 7.03714 11.8813 7.04566C11.8468 7.04913 11.8127 7.05426 11.7784 7.05885C11.6972 7.06953 11.6165 7.08333 11.5364 7.10023C11.5008 7.10779 11.4654 7.11596 11.4299 7.12474C11.3514 7.14437 11.2737 7.16698 11.197 7.1925C11.1618 7.20406 11.1267 7.21509 11.0919 7.22789C11.0134 7.25683 10.9364 7.29003 10.8599 7.32522C10.8289 7.33946 10.7973 7.35196 10.7666 7.36723C10.6616 7.41958 10.5583 7.47656 10.4581 7.54073C10.3674 7.59882 10.2796 7.6613 10.1949 7.72796C10.1112 7.79388 10.0308 7.86386 9.95399 7.93765C9.49512 8.37852 9.17646 8.94484 9.03725 9.58035C8.85145 10.4277 9.0069 11.2967 9.47467 12.027C9.53005 12.1135 9.58935 12.1959 9.65123 12.2753C9.67277 12.303 9.69624 12.3285 9.71854 12.3554C9.76032 12.4055 9.80234 12.4554 9.8466 12.5025C9.87538 12.5332 9.9056 12.5621 9.93545 12.5915C9.97631 12.6317 10.0175 12.6714 10.0602 12.7091C10.0933 12.7383 10.127 12.7667 10.1613 12.7944C10.204 12.8293 10.2477 12.863 10.2922 12.8955C10.3274 12.9213 10.3627 12.9468 10.399 12.9712C10.4478 13.004 10.4978 13.0346 10.5481 13.0645C10.5814 13.0843 10.6142 13.1049 10.6482 13.1235C10.7187 13.162 10.7906 13.1972 10.8635 13.2303C10.879 13.2373 10.8938 13.2457 10.9095 13.2525C11.0024 13.2929 11.0972 13.3284 11.1932 13.36C11.2088 13.3651 11.2248 13.3689 11.2405 13.3738C11.3209 13.3989 11.4022 13.4209 11.4843 13.4398C11.4944 13.4421 11.5041 13.4457 11.5143 13.4479C11.5304 13.4514 11.5467 13.4526 11.5629 13.4559C11.642 13.4719 11.7214 13.4849 11.8016 13.495C11.8368 13.4995 11.8718 13.5043 11.907 13.5077C11.9793 13.5143 12.0518 13.5176 12.1246 13.5194C12.155 13.5203 12.1855 13.5244 12.2159 13.5244C12.2322 13.5244 12.2481 13.5212 12.2643 13.5209C12.3714 13.519 12.4785 13.5107 12.5856 13.498C12.6347 13.4923 12.6837 13.4868 12.7325 13.4788C12.8395 13.4611 12.9456 13.4381 13.0503 13.4097C13.1068 13.3945 13.1623 13.3763 13.2181 13.358C13.2417 13.3502 13.2657 13.3444 13.2891 13.3361V15.0072H13.2892Z" fill="white"/>
    </svg>
  ),
  PlanModification: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_19828_21407)">
      <path fillRule="evenodd" clipRule="evenodd" d="M7.56395 0.616212C6.79175 0.616212 6.03809 1.00897 5.61995 1.71781C5.15735 2.50297 5.23043 3.46327 5.72489 4.16797L3.46283 6.40357C3.46014 6.40265 3.45744 6.40175 3.45473 6.40087C2.40299 5.85889 1.08431 6.22105 0.47645 7.25227C-0.15355 8.32057 0.20645 9.71575 1.27475 10.3458C1.89809 10.7133 2.63249 10.7418 3.26141 10.4908L5.55461 13.9355C5.20109 14.3346 4.98383 14.8566 4.98383 15.4272C4.98383 16.6674 6.00245 17.6862 7.24247 17.6862C8.38691 17.6862 9.33227 16.8159 9.47249 15.7074L9.85715 15.5706L9.60155 14.8524L9.43109 14.9133C9.35983 14.6123 9.22783 14.3292 9.04319 14.0811L9.07631 14.1067L14.9593 6.86059C15.1711 6.92867 15.3919 6.96471 15.6145 6.96751L15.6017 7.21951L16.3624 7.25677L16.3822 6.85213C16.8824 6.68617 17.3279 6.35011 17.6143 5.86429C18.2227 4.83235 17.9006 3.50179 16.9155 2.84443C16.8847 2.81673 16.8512 2.79213 16.8156 2.77099C16.7791 2.7496 16.7406 2.73193 16.7006 2.71825C15.6488 2.17627 14.3302 2.53843 13.7221 3.56965C13.7099 3.59035 13.7005 3.61195 13.6892 3.63301L9.81845 2.78431C9.78821 2.08213 9.43091 1.40497 8.81297 0.992772C8.78229 0.965023 8.74895 0.940364 8.71343 0.919152C8.67701 0.897768 8.63854 0.880099 8.59859 0.866412C8.27862 0.70163 7.92386 0.615839 7.56395 0.616212ZM7.59995 1.86361C7.76087 1.86991 7.92305 1.91581 8.07371 2.00455C8.55539 2.28859 8.71055 2.89105 8.42651 3.37273C8.14265 3.85441 7.54037 4.00993 7.05851 3.72571C6.57701 3.44167 6.42131 2.83957 6.70535 2.35771C6.90065 2.02651 7.24643 1.84957 7.59995 1.86361ZM15.7023 3.71509C15.8694 3.72205 16.0321 3.77049 16.1759 3.85603C16.6576 4.14007 16.8133 4.74253 16.5292 5.22421C16.2452 5.70607 15.6427 5.86159 15.161 5.57755C14.6792 5.29351 14.5238 4.69105 14.8081 4.20937C15.0032 3.87817 15.3488 3.70105 15.7023 3.71509ZM2.45591 7.39789C2.61665 7.40437 2.77901 7.45009 2.92949 7.53901C3.41135 7.82305 3.56687 8.42515 3.28283 8.90701C2.99879 9.38851 2.39669 9.54421 1.91483 9.26017C1.43333 8.97613 1.27763 8.37367 1.56167 7.89199C1.75697 7.56079 2.10239 7.38403 2.45609 7.39807L2.45591 7.39789ZM15.5648 7.98019L15.4885 9.50209L16.2506 9.54007L16.3251 8.01745L15.5648 7.98019ZM15.4514 10.263L15.4427 10.4419C15.4287 10.4415 15.415 10.4397 15.401 10.4397C14.1608 10.4397 13.1418 11.4585 13.1418 12.6985C13.1418 12.995 13.2014 13.2781 13.307 13.5384L12.4754 13.8336L12.7301 14.5513L13.7219 14.2003C14.1367 14.6632 14.7361 14.9577 15.401 14.9577C16.598 14.9577 17.5795 14.0059 17.647 12.8247C17.6554 12.7832 17.6597 12.7409 17.6596 12.6985C17.6596 12.6566 17.6554 12.6147 17.647 12.5736C17.5959 11.6693 17.008 10.9011 16.1986 10.5895L16.2129 10.3018L15.4514 10.263ZM15.4011 11.6999C15.9602 11.6999 16.3998 12.1395 16.3998 12.6987C16.3998 13.2578 15.9602 13.6977 15.4011 13.6977C14.8419 13.6977 14.402 13.2578 14.402 12.6987C14.402 12.1395 14.8421 11.6999 15.4011 11.6999ZM11.7562 14.0887L10.3203 14.5988L10.5745 15.3165L12.0116 14.8071L11.756 14.0889L11.7562 14.0887ZM7.24229 14.4283C7.80155 14.4283 8.24147 14.8679 8.24147 15.4272C8.24147 15.9864 7.80155 16.4262 7.24247 16.4262C6.68321 16.4262 6.24347 15.9864 6.24347 15.4272C6.24347 14.8679 6.68303 14.4283 7.24229 14.4283Z" fill="#D81B60"/>
      </g>
      <defs>
      <clipPath id="clip0_19828_21407">
      <rect width="18" height="18" fill="white"/>
      </clipPath>
      </defs>
    </svg>
  ),
  SoftDemand: () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0 2.5H15V4.16667C14.0833 4.16667 13.3333 4.91667 13.3333 5.83333V15.8333C13.3333 16.75 12.5833 17.5 11.6667 17.5H3.33333C2.41667 17.5 1.66667 16.75 1.66667 15.8333V5.83333C1.66667 4.91667 0.916667 4.16667 0 4.16667V2.5ZM3.33333 7.5V8.33333H5.83333V7.5H3.33333ZM3.33333 9.16667V10H5.83333V9.16667H3.33333ZM5.83333 13.3333V12.5H3.33333V13.3333H5.83333ZM7.5 11.6667V10.8333H3.33333V11.6667H7.5ZM7.5 6.66667V5.83333H3.33333V6.66667H7.5ZM17.9167 12.9167V14.1667H16.6667V12.9167H17.9167ZM16.25 8.75H15V8.33333C15 6.95 16.1167 5.83333 17.5 5.83333C18.8833 5.83333 20 6.95 20 8.33333C20 9.14167 19.5833 9.9 18.925 10.3417L18.675 10.5C18.2 10.8333 17.9167 11.35 17.9167 11.9167V12.0833H16.6667V11.9167C16.6667 10.925 17.1667 10 17.9917 9.45833L18.2333 9.3C18.5583 9.08333 18.75 8.725 18.75 8.33333C18.75 7.65 18.1917 7.08333 17.5 7.08333C16.8083 7.08333 16.25 7.64167 16.25 8.33333V8.75Z" fill="#000CED"/>
    </svg>
  ),
  FileClosure: () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M11.2949 3.39636C11.1773 3.34767 11.0348 3.33292 10.2856 3.33292H6.66634C6.20611 3.33292 5.83301 2.95982 5.83301 2.49959C5.83301 2.03935 6.20611 1.66626 6.66634 1.66626H10.2856C10.3245 1.66626 10.363 1.66619 10.4013 1.66613C10.967 1.66526 11.469 1.6645 11.9327 1.85656C12.3963 2.04861 12.7508 2.40415 13.1502 2.80476C13.1772 2.83186 13.2044 2.85916 13.2319 2.88664L15.4459 5.10069C15.4734 5.12816 15.5008 5.15539 15.5278 5.18241C15.9284 5.58178 16.284 5.93623 16.476 6.3999C16.6681 6.86357 16.6673 7.36561 16.6664 7.93128C16.6664 7.96955 16.6663 8.00811 16.6663 8.04696V11.6662C16.6663 12.1265 16.2933 12.4996 15.833 12.4996C15.3728 12.4996 14.9997 12.1265 14.9997 11.6662V8.04696C14.9997 7.29771 14.9849 7.15524 14.9363 7.03771C14.8876 6.92017 14.7973 6.809 14.2674 6.2792L12.0534 4.06516C11.5236 3.53536 11.4124 3.44504 11.2949 3.39636Z" fill="#323232"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M3.08958 1.91107C2.76414 1.58563 2.23651 1.58563 1.91107 1.91107C1.58563 2.23651 1.58563 2.76414 1.91107 3.08958L3.33366 4.51217V14.2218C3.33362 14.9606 3.33358 15.5971 3.40211 16.1068C3.47555 16.6531 3.64118 17.1767 4.06589 17.6014C4.49061 18.0262 5.01423 18.1917 5.5605 18.2652C6.07022 18.3337 6.70674 18.3337 7.44548 18.3337H14.167C14.9818 18.3337 15.7056 17.9438 16.162 17.3405L16.9111 18.0896C17.2365 18.415 17.7642 18.415 18.0896 18.0896C18.415 17.7642 18.415 17.2365 18.0896 16.9111L3.08958 1.91107Z" fill="#323232"/>
    </svg>
  ),
  ContextSummarization: () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="20" height="20" rx="4" fill="#00BCD4"/>
      <path d="M17.2583 13.9253L15.2417 11.9087C15.1642 11.8306 15.072 11.7686 14.9705 11.7262C14.8689 11.6839 14.76 11.6622 14.65 11.6622C14.54 11.6622 14.4311 11.6839 14.3295 11.7262C14.228 11.7686 14.1358 11.8306 14.0583 11.9087L11.075 14.892C10.9978 14.9699 10.9367 15.0622 10.8952 15.1637C10.8537 15.2653 10.8327 15.374 10.8333 15.4837V17.5003C10.8333 17.7213 10.9211 17.9333 11.0774 18.0896C11.2337 18.2459 11.4457 18.3337 11.6667 18.3337H13.6833C13.793 18.3343 13.9017 18.3133 14.0033 18.2718C14.1048 18.2303 14.1971 18.1692 14.275 18.092L17.2583 15.1087C17.3364 15.0312 17.3984 14.939 17.4407 14.8375C17.4831 14.7359 17.5048 14.627 17.5048 14.517C17.5048 14.407 17.4831 14.2981 17.4407 14.1965C17.3984 14.095 17.3364 14.0028 17.2583 13.9253ZM13.3333 16.667H12.5V15.8337L14.65 13.6837L15.4833 14.517L13.3333 16.667ZM8.33333 16.667H5C4.77899 16.667 4.56702 16.5792 4.41074 16.4229C4.25446 16.2666 4.16667 16.0547 4.16667 15.8337V4.16699C4.16667 3.94598 4.25446 3.73402 4.41074 3.57774C4.56702 3.42146 4.77899 3.33366 5 3.33366H9.16667V5.83366C9.16667 6.4967 9.43006 7.13259 9.8989 7.60143C10.3677 8.07027 11.0036 8.33366 11.6667 8.33366H14.1667V9.16699C14.1667 9.38801 14.2545 9.59997 14.4107 9.75625C14.567 9.91253 14.779 10.0003 15 10.0003C15.221 10.0003 15.433 9.91253 15.5893 9.75625C15.7455 9.59997 15.8333 9.38801 15.8333 9.16699V7.50033C15.8333 7.50033 15.8333 7.50033 15.8333 7.45033C15.8247 7.37377 15.8079 7.29835 15.7833 7.22533V7.15033C15.7433 7.06464 15.6898 6.98588 15.625 6.91699L10.625 1.91699C10.5561 1.85217 10.4774 1.79873 10.3917 1.75866C10.3668 1.75513 10.3415 1.75513 10.3167 1.75866L10.05 1.66699H5C4.33696 1.66699 3.70107 1.93038 3.23223 2.39923C2.76339 2.86807 2.5 3.50395 2.5 4.16699V15.8337C2.5 16.4967 2.76339 17.1326 3.23223 17.6014C3.70107 18.0703 4.33696 18.3337 5 18.3337H8.33333C8.55435 18.3337 8.76631 18.2459 8.92259 18.0896C9.07887 17.9333 9.16667 17.7213 9.16667 17.5003C9.16667 17.2793 9.07887 17.0674 8.92259 16.9111C8.76631 16.7548 8.55435 16.667 8.33333 16.667ZM10.8333 4.50866L12.9917 6.66699H11.6667C11.4457 6.66699 11.2337 6.5792 11.0774 6.42291C10.9211 6.26663 10.8333 6.05467 10.8333 5.83366V4.50866ZM6.66667 11.667H11.6667C11.8877 11.667 12.0996 11.5792 12.2559 11.4229C12.4122 11.2666 12.5 11.0547 12.5 10.8337C12.5 10.6126 12.4122 10.4007 12.2559 10.2444C12.0996 10.0881 11.8877 10.0003 11.6667 10.0003H6.66667C6.44565 10.0003 6.23369 10.0881 6.07741 10.2444C5.92113 10.4007 5.83333 10.6126 5.83333 10.8337C5.83333 11.0547 5.92113 11.2666 6.07741 11.4229C6.23369 11.5792 6.44565 11.667 6.66667 11.667ZM6.66667 8.33366H7.5C7.72101 8.33366 7.93297 8.24586 8.08926 8.08958C8.24554 7.9333 8.33333 7.72134 8.33333 7.50033C8.33333 7.27931 8.24554 7.06735 8.08926 6.91107C7.93297 6.75479 7.72101 6.66699 7.5 6.66699H6.66667C6.44565 6.66699 6.23369 6.75479 6.07741 6.91107C5.92113 7.06735 5.83333 7.27931 5.83333 7.50033C5.83333 7.72134 5.92113 7.9333 6.07741 8.08958C6.23369 8.24586 6.44565 8.33366 6.66667 8.33366ZM8.33333 13.3337H6.66667C6.44565 13.3337 6.23369 13.4215 6.07741 13.5777C5.92113 13.734 5.83333 13.946 5.83333 14.167C5.83333 14.388 5.92113 14.6 6.07741 14.7562C6.23369 14.9125 6.44565 15.0003 6.66667 15.0003H8.33333C8.55435 15.0003 8.76631 14.9125 8.92259 14.7562C9.07887 14.6 9.16667 14.388 9.16667 14.167C9.16667 13.946 9.07887 13.734 8.92259 13.5777C8.76631 13.4215 8.55435 13.3337 8.33333 13.3337Z" fill="white"/>
    </svg>
  ),
  AssetSearch: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_19827_21260)">
      <path d="M13.5 12H13.065L12.4575 11.3925C13.125 10.5 13.5 9.375 13.5 8.25C13.5 5.3475 11.1525 3 8.25 3C7.125 3 6 3.375 5.0925 4.05C2.775 5.79 2.3025 9.0825 4.0425 11.4C5.7825 13.7175 9.075 14.19 11.3925 12.45L12 13.0575V13.5L15.75 17.25L17.25 15.75L13.5 12ZM8.25 12C6.18 12 4.5 10.32 4.5 8.25C4.5 6.18 6.18 4.5 8.25 4.5C10.32 4.5 12 6.18 12 8.25C12 10.32 10.32 12 8.25 12ZM2.25 4.5L0.75 6V0.75H6L4.5 2.25H2.25V4.5ZM15.75 0.75V6L14.25 4.5V2.25H12L10.5 0.75H15.75ZM4.5 14.25L6 15.75H0.75V10.5L2.25 12V14.25H4.5Z" fill="#0056B3"/>
      </g>
      <defs>
      <clipPath id="clip0_19827_21260">
      <rect width="18" height="18" fill="white"/>
      </clipPath>
      </defs>
    </svg>
  ),
  AccountRehabilitation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.375 0H3.875C3.37772 0 2.90081 0.189642 2.54917 0.527208C2.19754 0.864773 2 1.32261 2 1.8V16.2C2 16.6774 2.19754 17.1352 2.54917 17.4728C2.90081 17.8104 3.37772 18 3.875 18H15.125C15.6223 18 16.0992 17.8104 16.4508 17.4728C16.8025 17.1352 17 16.6774 17 16.2V5.4L11.375 0ZM9.5 14.4C7.57812 14.4 5.92813 13.284 5.20625 11.7H6.80938C7.4 12.51 8.38438 13.05 9.5 13.05C10.3702 13.05 11.2048 12.7181 11.8202 12.1274C12.4355 11.5366 12.7812 10.7354 12.7812 9.9C12.7812 9.06457 12.4355 8.26335 11.8202 7.67261C11.2048 7.08187 10.3702 6.75 9.5 6.75C8.23438 6.75 7.15625 7.452 6.59375 8.46L8.09375 9.9H4.34375V6.3L5.5625 7.47C6.39688 6.228 7.84062 5.4 9.5 5.4C10.7432 5.4 11.9355 5.87411 12.8146 6.71802C13.6936 7.56193 14.1875 8.70653 14.1875 9.9C14.1875 11.0935 13.6936 12.2381 12.8146 13.082C11.9355 13.9259 10.7432 14.4 9.5 14.4Z" fill="#4CAF50"/>
    </svg>
  ),
  CaseReassignment: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="white"/>
      <path d="M16.5 9C16.5 4.875 13.125 1.5 9 1.5C4.875 1.5 1.5 4.875 1.5 9C1.5 13.125 4.875 16.5 9 16.5C13.125 16.5 16.5 13.125 16.5 9ZM11.25 4.875L13.875 7.5L11.25 10.125V8.25H8.25V6.75H11.25V4.875ZM6.75 13.125L4.125 10.5L6.75 7.875V9.75H9.75V11.25H6.75V13.125Z" fill="#2196F3"/>
    </svg>
  ),
  ManagerEscalation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#FF9800"/>
      <path d="M15 6H14.2125L5.2125 15H3C2.175 15 1.5 14.3325 1.5 13.5C1.5 12.675 2.175 12 3 12H3.9675L5.25 10.7175V7.5C5.25 7.0875 5.5875 6.75 6 6.75H6.75C7.1625 6.75 7.5 7.0875 7.5 7.5V8.4675L12.9675 3H15C15.8325 3 16.5 3.6675 16.5 4.5C16.5 5.3325 15.8325 6 15 6ZM6.375 3.75C6.9975 3.75 7.5 4.2525 7.5 4.875C7.5 5.4975 6.9975 6 6.375 6C5.7525 6 5.25 5.4975 5.25 4.875C5.25 4.2525 5.7525 3.75 6.375 3.75ZM15.1275 11.745L10.995 15.8775L9.6225 14.505L13.755 10.3725L12.375 9H16.5V13.125L15.1275 11.745Z" fill="white"/>
    </svg>
  ),
  DecisionSupport: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#FF9800"/>
      <path d="M15 6H14.2125L5.2125 15H3C2.175 15 1.5 14.3325 1.5 13.5C1.5 12.675 2.175 12 3 12H3.9675L5.25 10.7175V7.5C5.25 7.0875 5.5875 6.75 6 6.75H6.75C7.1625 6.75 7.5 7.0875 7.5 7.5V8.4675L12.9675 3H15C15.8325 3 16.5 3.6675 16.5 4.5C16.5 5.3325 15.8325 6 15 6ZM6.375 3.75C6.9975 3.75 7.5 4.2525 7.5 4.875C7.5 5.4975 6.9975 6 6.375 6C5.7525 6 5.25 5.4975 5.25 4.875C5.25 4.2525 5.7525 3.75 6.375 3.75ZM15.1275 11.745L10.995 15.8775L9.6225 14.505L13.755 10.3725L12.375 9H16.5V13.125L15.1275 11.745Z" fill="white"/>
    </svg>
  ),
  CustomerEngagement: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <rect width="18" height="18" rx="4" fill="#1976D2"/>
      <rect x="2" y="2" width="15" height="15" fill="url(#pattern0_19828_21272)"/>
      <defs>
      <pattern id="pattern0_19828_21272" patternContentUnits="objectBoundingBox" width="1" height="1">
      <use xlinkHref="#image0_19828_21272" transform="scale(0.01)"/>
      </pattern>
      <image id="image0_19828_21272" width="100" height="100" preserveAspectRatio="none" xlinkHref="data:image/png;base64,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"/>
      </defs>
    </svg>
  ),
  PaymentNegotiation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#388E3C"/>
      <path d="M8.25005 4.49958H10.5L12.9675 2.02458C13.0373 1.95429 13.1202 1.89849 13.2116 1.86041C13.303 1.82234 13.401 1.80273 13.5 1.80273C13.5991 1.80273 13.6971 1.82234 13.7885 1.86041C13.8799 1.89849 13.9628 1.95429 14.0325 2.02458L15.9675 3.96708C16.1072 4.1076 16.1856 4.29769 16.1856 4.49583C16.1856 4.69397 16.1072 4.88406 15.9675 5.02458L14.25 6.74958H8.25005V8.24958C8.25005 8.4485 8.17103 8.63926 8.03038 8.77991C7.88973 8.92057 7.69896 8.99958 7.50005 8.99958C7.30114 8.99958 7.11037 8.92057 6.96972 8.77991C6.82907 8.63926 6.75005 8.4485 6.75005 8.24958V5.99958C6.75005 5.60176 6.90808 5.22023 7.18939 4.93892C7.47069 4.65762 7.85222 4.49958 8.25005 4.49958ZM3.75005 8.24958V11.2496L2.03255 12.9671C1.89286 13.1076 1.81445 13.2977 1.81445 13.4958C1.81445 13.694 1.89286 13.8841 2.03255 14.0246L3.96755 15.9671C4.03727 16.0374 4.12022 16.0932 4.21162 16.1313C4.30301 16.1693 4.40104 16.1889 4.50005 16.1889C4.59906 16.1889 4.69709 16.1693 4.78848 16.1313C4.87987 16.0932 4.96283 16.0374 5.03255 15.9671L8.25005 12.7496H11.25C11.449 12.7496 11.6397 12.6706 11.7804 12.5299C11.921 12.3893 12 12.1985 12 11.9996V11.2496H12.75C12.949 11.2496 13.1397 11.1706 13.2804 11.0299C13.421 10.8893 13.5 10.6985 13.5 10.4996V9.74958H14.25C14.449 9.74958 14.6397 9.67057 14.7804 9.52991C14.921 9.38926 15 9.1985 15 8.99958V8.24958H9.75005V8.99958C9.75005 9.39741 9.59201 9.77894 9.31071 10.0602C9.0294 10.3415 8.64787 10.4996 8.25005 10.4996H6.75005C6.35222 10.4996 5.97069 10.3415 5.68939 10.0602C5.40808 9.77894 5.25005 9.39741 5.25005 8.99958V6.74958L3.75005 8.24958Z" fill="white"/>
    </svg>
  ),
  AccountReconciliation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M3 3.75V14.25C3 15.4927 4.00736 16.5 5.25 16.5H12.75C13.9927 16.5 15 15.4927 15 14.25V6.75C15 5.50736 13.9927 4.5 12.75 4.5H3.75C3.33579 4.5 3 4.16421 3 3.75ZM5.4375 9C5.4375 8.68935 5.68934 8.4375 6 8.4375H12C12.3106 8.4375 12.5625 8.68935 12.5625 9C12.5625 9.31065 12.3106 9.5625 12 9.5625H6C5.68934 9.5625 5.4375 9.31065 5.4375 9ZM5.4375 11.625C5.4375 11.3143 5.68934 11.0625 6 11.0625H10.125C10.4356 11.0625 10.6875 11.3143 10.6875 11.625C10.6875 11.9356 10.4356 12.1875 10.125 12.1875H6C5.68934 12.1875 5.4375 11.9356 5.4375 11.625Z" fill="#1C274D"/>
      <path d="M3.30664 3.06557C3.568 3.18278 3.75005 3.44525 3.75005 3.75024H12.75C13.009 3.75024 13.2603 3.78306 13.5 3.84476V3.22977C13.5 2.31695 12.6915 1.61576 11.7879 1.74485L3.68974 2.90173C3.54539 2.92236 3.41458 2.98055 3.30664 3.06557Z" fill="#1C274D"/>
    </svg>
  ),
  ComplianceReview: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#455A64"/>
      <path d="M14 13.5C14 14.6046 13.1046 15.5 12 15.5H6C4.89543 15.5 4 14.6046 4 13.5V4.83333C4 4.14298 4.55964 3.58333 5.25 3.58333H5.95833C6.25749 3.58333 6.5 3.34082 6.5 3.04167C6.5 2.74251 6.74251 2.5 7.04167 2.5H10.9583C11.2575 2.5 11.5 2.74251 11.5 3.04167C11.5 3.34082 11.7425 3.58333 12.0417 3.58333H12.75C13.4404 3.58333 14 4.14298 14 4.83333V13.5ZM5 12.4167C5 13.5212 5.89543 14.4167 7 14.4167H11C12.1046 14.4167 13 13.5212 13 12.4167V5.41667C13 5.00245 12.6642 4.66667 12.25 4.66667C11.8358 4.66667 11.5 5.00245 11.5 5.41667V5.47917C11.5 5.9279 11.1362 6.29167 10.6875 6.29167H7.3125C6.86377 6.29167 6.5 5.9279 6.5 5.47917V5.41667C6.5 5.00245 6.16421 4.66667 5.75 4.66667C5.33579 4.66667 5 5.00245 5 5.41667V12.4167ZM7.5 4.39583C7.5 4.84456 7.86377 5.20833 8.3125 5.20833H9.6875C10.1362 5.20833 10.5 4.84456 10.5 4.39583C10.5 3.9471 10.1362 3.58333 9.6875 3.58333H8.3125C7.86377 3.58333 7.5 3.9471 7.5 4.39583ZM9.49888 11.3845C8.96061 11.9677 8.03938 11.9677 7.50112 11.3845L6.97204 10.8114C6.79017 10.6143 6.79017 10.3107 6.97204 10.1136C7.17571 9.89298 7.52429 9.89298 7.72796 10.1136L7.925 10.3271C8.23485 10.6628 8.76515 10.6628 9.075 10.3271L10.772 8.48862C10.9757 8.26798 11.3243 8.26798 11.528 8.48862C11.7098 8.68565 11.7098 8.98935 11.528 9.18638L9.49888 11.3845Z" fill="white"/>
    </svg>
  ),
  PerformanceBenchmarking: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M0 2C0 0.89543 0.89543 0 2 0H16C17.1046 0 18 0.89543 18 2V16C18 17.1046 17.1046 18 16 18H2C0.89543 18 0 17.1046 0 16V2ZM10.9389 4.6547C10.5859 3.77223 9.3206 3.82154 9.0371 4.72855L7.2853 10.3345L6.89719 9.5583C6.72611 9.2161 6.37639 9 5.99382 9H4C3.44772 9 3 9.4477 3 10C3 10.5523 3.44772 11 4 11H5.38197L6.59448 13.425C7.001 14.2381 8.1907 14.1422 8.4619 13.2746L10.1158 7.9821L11.069 10.3651C11.2224 10.7486 11.5938 11 12.0068 11H14C14.5523 11 15 10.5523 15 10C15 9.4477 14.5523 9 14 9H12.677L10.9389 4.6547Z" fill="#E31B23"/>
    </svg>
  ),
  FinalDemand: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#FF3E00"/>
      <path d="M9.90909 10.4091H9.59091C9.03862 10.4091 8.59091 9.96138 8.59091 9.40909V8.11364C8.59091 7.56135 9.03862 7.11364 9.59091 7.11364H9.90909M9.90909 13.0455H9.25C8.88599 13.0455 8.59091 12.7504 8.59091 12.3864C8.59091 12.0224 8.88599 11.7273 9.25 11.7273H9.90909M2.86902 13.5217C2.48306 14.1884 2.96411 15.0227 3.73445 15.0227H14.7656C15.5359 15.0227 16.0169 14.1884 15.631 13.5217L10.1154 3.99483C9.73026 3.32954 8.76974 3.32954 8.38457 3.99483L2.86902 13.5217Z" fill="white"/>
    </svg>
  ),
  LegalEscalation: () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="18" height="18" rx="4" fill="#5D40B2"/>
      <path d="M9.06564 2.25C8.17605 2.25 7.38452 2.81037 7.09033 3.65093H2.76147V5.05185H4.12737L2.061 9.9551C1.73179 11.356 2.76147 12.0565 4.51262 12.0565C6.26378 12.0565 7.35651 11.356 6.96425 9.9551L4.89788 5.05185H7.08333C7.31448 5.64725 7.76978 6.10255 8.36517 6.3337V14.1579H2.061V15.5588H16.0703V14.1579H9.7661V6.3267C10.3615 6.10255 10.8168 5.64725 11.0409 5.05185H13.2334L11.167 9.9551C10.8378 11.356 11.8675 12.0565 13.6186 12.0565C15.3698 12.0565 16.4625 11.356 16.0703 9.9551L14.0039 5.05185H15.3698V3.65093H11.0479C10.7467 2.81037 9.95522 2.25 9.06564 2.25ZM9.06564 3.65093C9.25141 3.65093 9.42958 3.72473 9.56094 3.85609C9.6923 3.98745 9.7661 4.16562 9.7661 4.35139C9.7661 4.53716 9.6923 4.71533 9.56094 4.84669C9.42958 4.97805 9.25141 5.05185 9.06564 5.05185C8.87986 5.05185 8.7017 4.97805 8.57033 4.84669C8.43897 4.71533 8.36517 4.53716 8.36517 4.35139C8.36517 4.16562 8.43897 3.98745 8.57033 3.85609C8.7017 3.72473 8.87986 3.65093 9.06564 3.65093ZM4.51262 7.32836L5.56332 9.9551H3.46193L4.51262 7.32836ZM13.6186 7.32836L14.6693 9.9551H12.568L13.6186 7.32836Z" fill="white"/>
    </svg>
  )
}

const EVENT_TYPE_ICONS: Record<string, React.ComponentType> = {
    'Asset Search': TickerIcons.AssetSearch,
    'Account Rehabilitation': TickerIcons.AccountRehabilitation,
    'Case Reassignment': TickerIcons.CaseReassignment,
    'Manager Escalation': TickerIcons.ManagerEscalation,
    'Decision Support': TickerIcons.DecisionSupport,
    'Customer Engagement': TickerIcons.CustomerEngagement,
    'Payment Negotiation': TickerIcons.PaymentNegotiation,
    'Account Reconciliation': TickerIcons.AccountReconciliation,
    'Compliance Review': TickerIcons.ComplianceReview,
    'Performance Benchmarking': TickerIcons.PerformanceBenchmarking,
    'Final Demand': TickerIcons.FinalDemand,
    'Legal Escalation': TickerIcons.LegalEscalation,
    'Outcome Reporting': TickerIcons.OutcomeReporting,
    'Colleague Consultation': TickerIcons.ColleagueConsultation,
    'Information Verification': TickerIcons.InformationVerification,
    'Case Evaluation': TickerIcons.CaseEvaluation,
    'Plan Modification': TickerIcons.PlanModification,
    'Soft Demand': TickerIcons.SoftDemand,
    'File Closure': TickerIcons.FileClosure,
    'Context Summarization': TickerIcons.ContextSummarization,
  };

  export const getEventTypeIcon = (eventType: string) => {
      const IconComponent = EVENT_TYPE_ICONS[eventType];
      return IconComponent ? <IconComponent /> : null;
    };