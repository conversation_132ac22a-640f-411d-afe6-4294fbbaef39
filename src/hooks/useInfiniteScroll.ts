import { useCallback, useEffect, useRef, useState } from 'react';

import { ChatHistoryItem, PaginatedChatHistoryResponse } from '@/types/agents';

interface UseInfiniteScrollOptions {
  pageSize?: number;
  scrollThreshold?: number;
  isServiceReady?: boolean;
}

interface UseInfiniteScrollState {
  items: ChatHistoryItem[];
  currentPage: number;
  totalItems: number;
  hasNextPage: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

interface UseInfiniteScrollReturn extends UseInfiniteScrollState {
  loadMore: () => Promise<void>;
  reset: () => void;
  handleScroll: (event: React.UIEvent<HTMLDivElement>) => void;
}

export const useInfiniteScroll = (
  fetchFunction: (
    page: number,
    pageSize: number
  ) => Promise<PaginatedChatHistoryResponse>,
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn => {
  const {
    pageSize = 20,
    scrollThreshold = 200,
    isServiceReady = true,
  } = options;

  const [state, setState] = useState<UseInfiniteScrollState>({
    items: [],
    currentPage: 0,
    totalItems: 0,
    hasNextPage: true,
    isLoading: false,
    isLoadingMore: false,
    error: null,
  });

  const prefetchedPages = useRef<Set<number>>(new Set());
  const isLoadingRef = useRef(false);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const lastScrollTriggerTime = useRef<number>(0);
  const pendingLoadRef = useRef<number | null>(null);

  const loadPage = useCallback(
    async (page: number, isInitial = false) => {
      // Prevent duplicate loads and infinite loops
      if (isLoadingRef.current || prefetchedPages.current.has(page)) {
        console.log(
          `Skipping load for page ${page} - already loading or prefetched`
        );
        return;
      }

      // Don't load if we don't have next page and it's not the initial load
      if (!isInitial && !state.hasNextPage) {
        console.log(`Skipping load for page ${page} - no more pages available`);
        return;
      }

      // Prevent loading the same page that's already pending
      if (pendingLoadRef.current === page) {
        console.log(`Skipping load for page ${page} - already pending`);
        return;
      }

      isLoadingRef.current = true;
      pendingLoadRef.current = page;
      prefetchedPages.current.add(page);

      // Store scroll position before loading new content (for maintaining position)
      const scrollContainer = scrollContainerRef.current;
      const previousScrollHeight = scrollContainer?.scrollHeight || 0;
      const previousScrollTop = scrollContainer?.scrollTop || 0;

      setState(prev => ({
        ...prev,
        isLoading: isInitial,
        isLoadingMore: !isInitial,
        error: null,
      }));

      try {
        const response = await fetchFunction(page, pageSize);

        if (response.status && response.data) {
          const { items, total, page: responsePage } = response.data;

          setState(prev => {
            // For chat history, we want newest messages at the bottom
            // So we prepend older messages (higher page numbers) to the beginning
            const newItems = page === 1 ? items : [...items, ...prev.items];

            return {
              ...prev,
              items: newItems,
              currentPage: Math.max(prev.currentPage, responsePage),
              totalItems: total,
              hasNextPage: newItems.length < total,
              isLoading: false,
              isLoadingMore: false,
            };
          });

          // Restore scroll position after DOM update for non-initial loads
          if (!isInitial && scrollContainer) {
            // Use multiple requestAnimationFrame calls to ensure DOM is fully updated
            requestAnimationFrame(() => {
              requestAnimationFrame(() => {
                const newScrollHeight = scrollContainer.scrollHeight;
                const heightDifference = newScrollHeight - previousScrollHeight;

                // Maintain relative scroll position by adjusting for new content height
                if (heightDifference > 0) {
                  // Calculate the new scroll position to maintain the user's view
                  const newScrollTop = previousScrollTop + heightDifference;

                  // Set the scroll position without smooth scrolling to avoid visual jumps
                  scrollContainer.scrollTop = newScrollTop;
                }
              });
            });
          }
        } else {
          // If response is not successful, clear loading states
          setState(prev => ({
            ...prev,
            isLoading: false,
            isLoadingMore: false,
          }));
        }
      } catch (error) {
        console.error('Failed to load page:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
          error:
            error instanceof Error ? error.message : 'Failed to load messages',
        }));
        // Remove from prefetched pages so we can retry
        prefetchedPages.current.delete(page);
      } finally {
        isLoadingRef.current = false;
        pendingLoadRef.current = null;
      }
    },
    [fetchFunction, pageSize, state.hasNextPage]
  );

  const loadMore = useCallback(async () => {
    if (!state.hasNextPage || isLoadingRef.current || !isServiceReady) {
      return;
    }

    const nextPage = state.currentPage + 1;
    await loadPage(nextPage);
  }, [state.hasNextPage, state.currentPage, loadPage, isServiceReady]);

  // Prefetching disabled to prevent infinite loops
  // const prefetchNextPages = useCallback(async () => { ... }, [...]);

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const container = event.currentTarget;
      scrollContainerRef.current = container;

      // Check if user scrolled near the top (for loading older messages)
      const { scrollTop } = container;
      const currentTime = Date.now();

      // Throttle scroll events to prevent rapid-fire loads
      if (currentTime - lastScrollTriggerTime.current < 300) {
        return;
      }

      if (
        scrollTop <= scrollThreshold &&
        state.hasNextPage &&
        !isLoadingRef.current &&
        isServiceReady
      ) {
        lastScrollTriggerTime.current = currentTime;
        loadMore();
      }

      // Disable prefetching to prevent interference with manual loads
      // Prefetching can cause infinite loops when combined with scroll-triggered loads
      /*
      if (
        scrollTop <= scrollThreshold * 2 &&
        state.hasNextPage &&
        isServiceReady
      ) {
        prefetchNextPages();
      }
      */
    },
    [scrollThreshold, state.hasNextPage, loadMore, isServiceReady]
  );

  const reset = useCallback(() => {
    setState({
      items: [],
      currentPage: 0,
      totalItems: 0,
      hasNextPage: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
    });
    prefetchedPages.current.clear();
    isLoadingRef.current = false;
    pendingLoadRef.current = null;
    lastScrollTriggerTime.current = 0;
  }, []);

  // Initial load - only trigger once when component mounts and service is ready
  useEffect(() => {
    if (
      state.items.length === 0 &&
      !state.isLoading &&
      state.hasNextPage &&
      !isLoadingRef.current &&
      isServiceReady
    ) {
      // Add a delay to allow Axios instance to initialize
      const timeoutId = setTimeout(() => {
        loadPage(1, true);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [isServiceReady, loadPage]); // Include isServiceReady and loadPage in dependencies

  return {
    ...state,
    loadMore,
    reset,
    handleScroll,
  };
};
