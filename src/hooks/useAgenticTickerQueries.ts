import { useQuery, useQueryClient } from '@tanstack/react-query';

import {
  fetchTaskTypeReferences,
  useAgenticTickerApi,
} from '@/services/agenticTickerService';
import {
  AgenticTickerApiResponse,
  FilterState,
  TaskTypeReferencesApiResponse,
} from '@/types/agenticTicker';

// Query keys factory
export const AGENTIC_TICKER_QUERY_KEYS = {
  all: ['agenticTicker'] as const,
  lists: () => [...AGENTIC_TICKER_QUERY_KEYS.all, 'list'] as const,
  list: (filters: {
    suites?: string[];
    agents?: string[];
    taskTypes?: string[];
    statuses?: string[];
    dateFrom?: string;
    dateTo?: string;
    search?: string;
    page?: number;
    pageSize?: number;
  }) => [...AGENTIC_TICKER_QUERY_KEYS.lists(), filters] as const,
  taskTypes: () => [...AGENTIC_TICKER_QUERY_KEYS.all, 'taskTypes'] as const,
};

interface UseAgenticTickerQueryParams {
  filters: FilterState;
  currentPage: number;
  pageSize?: number;
  enabled?: boolean;
  refetchInterval?: number;
}

export const useAgenticTickerQuery = ({
  filters,
  currentPage,
  pageSize = 10,
  enabled = true,
  refetchInterval = 15000, // 15 seconds auto-refresh
}: UseAgenticTickerQueryParams) => {
  const { fetchAgenticTickerData } = useAgenticTickerApi();

  // Convert filters to API params
  const apiParams = {
    suites: filters.suites,
    agents: filters.agents,
    taskTypes: filters.eventTypes,
    statuses: filters.statuses,
    dateFrom: filters.dateRange.startDate?.toISOString(),
    dateTo: filters.dateRange.endDate?.toISOString(),
    search: filters.searchQuery.trim() || undefined,
    page: currentPage,
    pageSize,
  };

  return useQuery({
    queryKey: AGENTIC_TICKER_QUERY_KEYS.list(apiParams),
    queryFn: () => fetchAgenticTickerData(apiParams),
    enabled,
    refetchInterval,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: false,
    staleTime: 10000, // Consider data stale after 10 seconds
    cacheTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    select: (data: AgenticTickerApiResponse) => {
      if (!data.status || !data.data) {
        throw new Error(data.message || 'Failed to fetch data');
      }

      // Convert API data to UI format
      const convertedEntries = data.data.items.map((apiEntry, index) => ({
        id: `entry-${apiEntry.taskId}-${index}`,
        eventId: apiEntry.taskId,
        owner: {
          name: apiEntry.owner.agentName,
          image: '', // Will be populated by avatar resolution logic
          role: apiEntry.owner.role,
          suiteName: apiEntry.owner.suiteName,
          suiteFunction: apiEntry.owner.suiteFunction,
        },
        receiver: {
          name: apiEntry.receiver.agentName,
          image: '', // Will be populated by avatar resolution logic
          isExternal: false, // Assuming internal for now, can be enhanced
          role: apiEntry.receiver.role,
          suiteName: apiEntry.receiver.suiteName,
          suiteFunction: apiEntry.receiver.suiteFunction,
        },
        eventType: apiEntry.taskType,
        timestamp: new Date(apiEntry.createdAt),
        status: apiEntry.status,
      }));

      return {
        items: convertedEntries,
        total: data.data.total,
        page: data.data.page,
        pageSize: data.data.pageSize,
      };
    },
  });
};

export const useTaskTypeReferencesQuery = (enabled = true) => {
  return useQuery({
    queryKey: AGENTIC_TICKER_QUERY_KEYS.taskTypes(),
    queryFn: fetchTaskTypeReferences,
    enabled,
    staleTime: 10 * 60 * 1000, // Task types rarely change, keep fresh for 10 minutes
    cacheTime: 30 * 60 * 1000, // Keep cached for 30 minutes
    refetchOnWindowFocus: false,
    retry: 3,
    select: (data: TaskTypeReferencesApiResponse) => {
      if (!data.status || !data.data) {
        throw new Error(data.message || 'Failed to fetch task types');
      }
      return data.data;
    },
  });
};

// Hook for manual refresh functionality
export const useAgenticTickerRefresh = () => {
  const queryClient = useQueryClient();

  const refreshData = async (
    filters: FilterState,
    currentPage: number,
    pageSize = 10
  ) => {
    const apiParams = {
      suites: filters.suites,
      agents: filters.agents,
      taskTypes: filters.eventTypes,
      statuses: filters.statuses,
      dateFrom: filters.dateRange.startDate?.toISOString(),
      dateTo: filters.dateRange.endDate?.toISOString(),
      search: filters.searchQuery.trim() || undefined,
      page: currentPage,
      pageSize,
    };

    // Invalidate and refetch the specific query
    await queryClient.invalidateQueries({
      queryKey: AGENTIC_TICKER_QUERY_KEYS.list(apiParams),
    });
  };

  return { refreshData };
};
