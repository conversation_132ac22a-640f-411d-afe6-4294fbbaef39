import { useTenant } from '@/context/TenantContext';
import { publicRequest } from '@/lib/axios/publicRequest';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import {
  AgenticTickerApiResponse,
  TaskTypeReferencesApiResponse,
} from '@/types/agenticTicker';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

// Hook for authenticated agentic ticker API calls
export const useAgenticTickerApi = () => {
  const { tenantId, activeAgent } = useTenant();

  const axiosInstanceRef = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || '',
    activeAgent || ''
  );

  const fetchAgenticTickerData = async (params: {
    suites?: string[];
    agents?: string[];
    taskTypes?: string[];
    statuses?: string[];
    dateFrom?: string;
    dateTo?: string;
    search?: string;
    page?: number;
    pageSize?: number;
  }): Promise<AgenticTickerApiResponse> => {
    const searchParams = new URLSearchParams();

    // Add array parameters
    params.suites?.forEach(suite => searchParams.append('suites', suite));
    params.agents?.forEach(agent => searchParams.append('agents', agent));
    params.taskTypes?.forEach(taskType =>
      searchParams.append('taskTypes', taskType)
    );
    params.statuses?.forEach(status => searchParams.append('statuses', status));

    // Add single parameters
    if (params.dateFrom) searchParams.append('dateFrom', params.dateFrom);
    if (params.dateTo) searchParams.append('dateTo', params.dateTo);
    if (params.search) searchParams.append('search', params.search);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.pageSize)
      searchParams.append('pageSize', params.pageSize.toString());

    const queryString = searchParams.toString();
    const url = `${agenticService}/agentic-ticker${queryString ? `?${queryString}` : ''}`;

    const response = await axiosInstanceRef.current?.get(url);
    return response?.data;
  };

  return {
    fetchAgenticTickerData,
    axiosInstanceRef,
  };
};

// Function for fetching task type references (no auth required)
export const fetchTaskTypeReferences =
  async (): Promise<TaskTypeReferencesApiResponse> => {
    const response = await publicRequest(BASE_URL).get(
      `${agenticService}/task-logs/public/task-type-references`
    );
    return response.data;
  };
